import React, { ReactNode } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import { Ionicons } from '@expo/vector-icons';

/**
 * 对话框类型定义
 * - info: 信息提示，蓝色图标
 * - success: 成功提示，绿色图标
 * - warning: 警告提示，橙色图标
 * - error: 错误提示，红色图标
 */
type DialogType = 'info' | 'success' | 'warning' | 'error';

interface CustomDialogProps {
  /** 控制对话框的显示和隐藏 */
  visible: boolean;
  /** 对话框标题 */
  title: string;
  /** 对话框内容 */
  message?: string;
  /** 确认按钮文本，默认为 'OK' */
  confirmText?: string;
  /** 取消按钮文本，默认为 'Cancel' */
  cancelText?: string;
  /** 点击确认按钮时的回调函数 */
  onConfirm?: () => void;
  /** 点击取消按钮时的回调函数，如果不提供则不显示取消按钮 */
  onCancel?: () => void;
  /** 对话框类型，决定图标和主题色 */
  type?: DialogType;
  /** 确认按钮的加载状态 */
  confirmLoading?: boolean;
  /** 自定义内容，替代默认的消息展示 */
  customContent?: ReactNode;
}

export const CustomDialog: React.FC<CustomDialogProps> = ({
  visible,
  title,
  message,
  confirmText,
  cancelText,
  onConfirm,
  onCancel,
  type = 'info',
  confirmLoading = false,
  customContent,
}) => {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme );
  const [animation] = React.useState(new Animated.Value(0));

  React.useEffect(() => {
    if (visible) {
      // 显示动画：使用弹性动画效果
      Animated.spring(animation, {
        toValue: 1,
        useNativeDriver: true,
        friction: 8,
        tension: 40,
      }).start();
    } else {
      // 隐藏动画：线性淡出
      Animated.timing(animation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  // 根据对话框类型获取对应的图标
  const getIconName = (): keyof typeof Ionicons.glyphMap => {
    switch (type) {
      case 'success':
        return 'checkmark-circle';
      case 'warning':
        return 'warning';
      case 'error':
        return 'close-circle';
      default:
        return 'information-circle';
    }
  };

  // 根据对话框类型获取对应的颜色
  const getIconColor = () => {
    switch (type) {
      case 'success':
        return theme.system.success;
      case 'warning':
        return theme.system.warning;
      case 'error':
        return theme.system.error;
      default:
        return theme.system.info;
    }
  };

  // 缩放动画插值
  const scale = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0.8, 1],
  });

  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    container: {
      width: MODAL_WIDTH,
      backgroundColor: theme.system.background,
      borderRadius: 16,
      padding: 20,
      alignItems: 'center',
      ...Platform.select({
        ios: {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 4,
        },
        android: {
          elevation: 5,
        },
      }),
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 16,
      gap: 8,
    },
    iconContainer: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
    },
    title: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.onSurface,
      textAlign: 'center',
    },
    message: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 24,
      lineHeight: 22,
      textAlign: 'center',
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 12,
      width: '100%',
    },
    button: {
      flex: 1,
      height: 44,
      borderRadius: 22,
      justifyContent: 'center',
      alignItems: 'center',
      maxWidth: 140,
    },
    cancelButton: {
      backgroundColor: '#F5F5F5',
    },
    confirmButton: {
      backgroundColor: theme.colors.primary,
    },
    cancelButtonText: {
      color: theme.colors.onSurfaceVariant,
      fontSize: 16,
      fontWeight: '600',
    },
    confirmButtonText: {
      color: theme.colors.onPrimary,
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.container,
            {
              transform: [{ scale }],
            },
          ]}
        >
          <View style={styles.header}>
            <View style={[styles.iconContainer, { backgroundColor: `${getIconColor()}15` }]}>
              <Ionicons
                name={getIconName()}
                size={24}
                color={getIconColor()}
              />
            </View>
            <Text style={styles.title}>{title || t(`common.dialog.${type}`)}</Text>
          </View>
          {customContent ? (
            customContent
          ) : (
            <Text style={styles.message}>{message}</Text>
          )}
          <View style={styles.buttonContainer}>
            {onCancel && (
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={onCancel}
                disabled={confirmLoading}
              >
                <Text style={styles.cancelButtonText}>
                  {cancelText || t('common.cancel')}
                </Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity
              style={[styles.button, styles.confirmButton]}
              onPress={onConfirm}
              disabled={confirmLoading}
            >
              {confirmLoading ? (
                <ActivityIndicator color={theme.colors.onPrimary} size="small" />
              ) : (
                <Text style={styles.confirmButtonText}>
                  {confirmText || t('common.apply')}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const { width } = Dimensions.get('window');
const MODAL_WIDTH = Math.min(width * 0.85, 320); 