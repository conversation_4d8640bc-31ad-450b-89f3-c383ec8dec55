import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';

interface VerificationModalAction {
  text: string;
  onPress: () => void;
  variant: 'primary' | 'secondary';
  loading?: boolean;
}

interface MemberInfo {
  memberId?: string;
  memberName?: string;
  memberPhone?: string;
  role?: string;
  registrationStatus?: string;
}

interface EventInfo {
  eventId?: string;
  eventName?: string;
  eventDate?: string;
  eventLocation?: string;
}

interface VerificationModalProps {
  visible: boolean;
  onClose: () => void;
  isValid: boolean;
  title: string;
  errorMessage?: string;
  memberInfo?: MemberInfo;
  eventInfo?: EventInfo;
  customContent?: React.ReactNode;
  actions: VerificationModalAction[];
}

export const VerificationModal: React.FC<VerificationModalProps> = ({
  visible,
  onClose,
  isValid,
  title,
  errorMessage,
  memberInfo,
  eventInfo,
  customContent,
  actions,
}) => {
  const { t } = useTranslation();
  const [animation] = useState(new Animated.Value(0));
  const [modalVisible, setModalVisible] = useState(false);

  // Control modal animation and visibility
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    
    if (visible) {
      setModalVisible(true);
      // 添加短暂延迟，确保 camera view 完全消失
      timeoutId = setTimeout(() => {
        Animated.timing(animation, { 
          toValue: 1, 
          duration: 300, 
          useNativeDriver: true 
        }).start();
      }, 300); // 300ms 延迟
    } else {
      Animated.timing(animation, { 
        toValue: 0, 
        duration: 200, 
        useNativeDriver: true 
      }).start(() => {
        setModalVisible(false);
      });
    }

    // 清理 timeout
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [visible]);

  const translateY = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [400, 0], // 增加初始位移距离，确保完全从屏幕下方进入
  });

  const opacity = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  const renderInfoRow = (label: string, value: string) => (
    <View style={styles.infoRow}>
      <Text style={styles.infoLabel}>{label}</Text>
      <Text style={styles.infoValue}>{value}</Text>
    </View>
  );

  const renderMemberInfo = () => {
    if (!memberInfo || Object.keys(memberInfo).length === 0) return null;

    return (
      <View style={styles.infoContainer}>
        {memberInfo.memberId && renderInfoRow(t('qrcode.verification.memberId'), memberInfo.memberId)}
        {memberInfo.memberName && renderInfoRow(t('qrcode.verification.memberName'), memberInfo.memberName)}
        {memberInfo.memberPhone && renderInfoRow(t('qrcode.verification.memberPhone'), memberInfo.memberPhone)}
        {memberInfo.role && renderInfoRow(t('qrcode.verification.role'), memberInfo.role)}
        {memberInfo.registrationStatus && renderInfoRow(t('qrcode.verification.status'), memberInfo.registrationStatus)}
      </View>
    );
  };

  const renderEventInfo = () => {
    if (!eventInfo || Object.keys(eventInfo).length === 0) return null;

    return (
      <View style={styles.infoContainer}>
        {eventInfo.eventName && renderInfoRow(t('qrcode.verification.eventName'), eventInfo.eventName)}
        {eventInfo.eventDate && renderInfoRow(t('qrcode.verification.eventDate'), eventInfo.eventDate)}
        {eventInfo.eventLocation && renderInfoRow(t('qrcode.verification.eventLocation'), eventInfo.eventLocation)}
      </View>
    );
  };

  return (
    <Modal
      transparent={true}
      visible={modalVisible}
      animationType="none"
      onRequestClose={() => {}}
    >
      <Animated.View style={[styles.modalContainer, { opacity }]}>
        <TouchableOpacity 
          style={styles.backdrop} 
          activeOpacity={1}
          onPress={() => {}} // 防止背景点击关闭
        />
        <Animated.View 
          style={[
            styles.content,
            { transform: [{ translateY }] }
          ]}
        >
          
          {/* Header */}
          <View style={styles.header}>
            {!isValid ? (
              <MaterialCommunityIcons name="close-circle" size={60} color="#f44336" />
            ) : (
              <MaterialCommunityIcons name="check-circle-outline" size={60} color="#4CAF50" />
            )}
            <Text style={styles.title}>{title}</Text>
          </View>
          
          {/* Body */}
          <View style={styles.body}>
            {!isValid ? (
              <Text style={styles.errorText}>
                {errorMessage}
              </Text>
            ) : (
              <>
                {renderMemberInfo()}
                {renderEventInfo()}
                {customContent}
              </>
            )}
          </View>
          
          {/* Actions */}
          <View style={styles.actions}>
            {actions.map((action, index) => (
              <TouchableOpacity 
                key={index}
                style={[
                  styles.button, 
                  action.variant === 'primary' ? styles.primaryButton : styles.secondaryButton
                ]}
                onPress={action.loading ? undefined : action.onPress}
                disabled={action.loading}
              >
                <Text style={
                  action.variant === 'primary' ? styles.primaryButtonText : styles.secondaryButtonText
                }>
                  {action.loading ? t('common.loading') : action.text}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
  },
  content: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 24,
    paddingBottom: Platform.OS === 'ios' ? 44 : 24,
    paddingTop: 24,
    alignItems: 'center',
    minHeight: 300,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -3 },
        shadowOpacity: 0.1,
        shadowRadius: 10,
      },
      android: {
        elevation: 10,
      },
    }),
  },

  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: '#333',
    marginTop: 16,
    textAlign: 'center',
  },
  body: {
    marginBottom: 24,
    alignItems: 'center',
    width: '100%',
  },
  errorText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 16,
  },
  infoContainer: {
    width: '100%',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    paddingBottom: 16,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#555',
    width: 100,
  },
  infoValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    flex: 1,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 12,
  },
  button: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#4CAF50',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4CAF50',
  },
}); 