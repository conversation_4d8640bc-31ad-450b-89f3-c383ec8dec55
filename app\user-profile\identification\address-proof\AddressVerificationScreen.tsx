import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, Platform, SafeAreaView } from 'react-native';
import { Image } from 'expo-image';
import { useTranslation } from 'react-i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { StatusBar } from 'expo-status-bar';
import { appStyleStore } from 'stores/app_style_store';
import { CustomDialog } from '@/common_modules/CustomDialog';
import { VerificationTypeEnum, VerificationStatusEnum } from 'types/enums';
import { useSubmitVerification, useFetchUserVerifications } from '@/api/user_services';
import { createTheme } from 'theme/index';

type UploadedFile = {
  uri: string;
  type: 'image' | 'pdf';
  name?: string;
};

export default function AddressVerificationScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  const router = useRouter();
  const { refetch: refetchVerifications } = useFetchUserVerifications();
  const submitVerificationMutation = useSubmitVerification();

  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successDialogVisible, setSuccessDialogVisible] = useState(false);
  const [errorDialogVisible, setErrorDialogVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.system.background,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
    },
    content: {
      padding: 24,
    },
    section: {
      flex: 1,
    },
    sectionDescription: {
      fontSize: 15,
      color: theme.system.secondaryText,
      marginBottom: 24,
    },
    uploadButton: {
      height: 200,
      borderWidth: 1,
      borderColor: theme.system.border,
      borderRadius: 12,
      borderStyle: 'dashed',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#F5F5F5',
    },
    uploadButtonWithFile: {
      borderStyle: 'solid',
    },
    uploadButtonText: {
      marginTop: 8,
      fontSize: 15,
      color: theme.colors.primary,
    },
    previewImage: {
      width: '100%',
      height: '100%',
      borderRadius: 12,
      resizeMode: 'contain',
    },
    pdfPreview: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    pdfName: {
      marginTop: 8,
      fontSize: 14,
      color: theme.system.text,
      maxWidth: '80%',
    },
    submitButton: {
      backgroundColor: theme.colors.primary,
      paddingVertical: 16,
      borderRadius: 12,
      alignItems: 'center',
      marginTop: 24,
    },
    submitButtonDisabled: {
      backgroundColor: '#CCCCCC',
    },
    submitButtonText: {
      fontSize: 17,
      fontWeight: '600',
      color: '#FFFFFF',
    },
    uploadOptions: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginBottom: 24,
    },
    uploadOption: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    uploadOptionText: {
      marginTop: 8,
      fontSize: 15,
      color: theme.colors.primary,
    },
    uploadedFile: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    uploadedImage: {
      width: 80,
      height: 80,
      borderRadius: 12,
      marginRight: 16,
    },
    uploadedPdf: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    uploadedPdfName: {
      marginTop: 8,
      fontSize: 14,
      color: theme.system.text,
      maxWidth: '80%',
    },
    removeButton: {
      marginLeft: 16,
    },
    sizeLimit: {
      fontSize: 14,
      color: theme.system.secondaryText,
      marginTop: 16,
    },
    submitSection: {
      marginTop: 'auto',
      paddingTop: 16,
    },
  });

  const requestImagePermissions = async () => {
    if (Platform.OS !== 'web') {
      const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
      const { status: libraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (cameraStatus !== 'granted') {
        Alert.alert(
          t('common.error'),
          t('identity.permissions.camera'),
          [{ text: t('common.ok') }]
        );
        return false;
      }
      
      if (libraryStatus !== 'granted') {
        Alert.alert(
          t('common.error'),
          t('identity.permissions.gallery'),
          [{ text: t('common.ok') }]
        );
        return false;
      }
    }
    return true;
  };

  const pickImage = async (method: 'camera' | 'gallery') => {
    const hasPermissions = await requestImagePermissions();
    if (!hasPermissions) return;

    try {
      const options: ImagePicker.ImagePickerOptions = {
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      };

      const result = method === 'camera'
        ? await ImagePicker.launchCameraAsync(options)
        : await ImagePicker.launchImageLibraryAsync(options);

      if (!result.canceled && result.assets[0].uri) {
        // Check file size (10MB limit)
        const fileInfo = await FileSystem.getInfoAsync(result.assets[0].uri);
        
        if (!fileInfo.exists) {
          setErrorMessage('Selected file does not exist');
          setErrorDialogVisible(true);
          return;
        }

        if (fileInfo.size > 10 * 1024 * 1024) {
          setErrorMessage(t('identity.address.upload.pdf.error'));
          setErrorDialogVisible(true);
          return;
        }

        setUploadedFile({
          uri: result.assets[0].uri,
          type: 'image',
        });
      }
    } catch (error) {
      setErrorMessage(method === 'camera'
        ? t('identity.errors.capture')
        : t('identity.errors.upload'));
      setErrorDialogVisible(true);
    }
  };

  const pickDocument = async () => {
    try {
      console.log('Starting PDF picker...');
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf'],
        multiple: false,
        copyToCacheDirectory: true,
      });

      console.log('Document picker result:', result);

      if (result.canceled) {
        console.log('Document picking was canceled');
        return;
      }

      const asset = result.assets[0];
      console.log('Selected asset:', asset);

      // Check file size (10MB limit)
      const fileInfo = await FileSystem.getInfoAsync(asset.uri);
      console.log('File info:', fileInfo);

      if (!fileInfo.exists) {
        console.error('File does not exist');
        Alert.alert(
          t('common.error'),
          'Selected file does not exist'
        );
        return;
      }

      if (fileInfo.size > 10 * 1024 * 1024) {
        console.log('File too large:', fileInfo.size);
        Alert.alert(
          t('common.error'),
          t('identity.address.upload.pdf.error')
        );
        return;
      }

      setUploadedFile({
        uri: asset.uri,
        type: 'pdf',
        name: asset.name,
      });
    } catch (error) {
      console.error('Error picking document:', error);
      Alert.alert(
        t('common.error'),
        error instanceof Error ? error.message : t('identity.errors.upload')
      );
    }
  };

  const renderUploadOptions = () => (
    <View style={styles.uploadOptions}>
      <TouchableOpacity
        style={styles.uploadOption}
        onPress={() => pickImage('camera')}
      >
        <MaterialCommunityIcons name="camera" size={24} color={theme.colors.primary} />
        <Text style={styles.uploadOptionText}>{t('identity.upload.camera')}</Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.uploadOption}
        onPress={() => pickImage('gallery')}
      >
        <MaterialCommunityIcons name="image" size={24} color={theme.colors.primary} />
        <Text style={styles.uploadOptionText}>{t('identity.upload.gallery')}</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.uploadOption}
        onPress={pickDocument}
      >
        <MaterialCommunityIcons name="file-pdf-box" size={24} color={theme.colors.primary} />
        <Text style={styles.uploadOptionText}>{t('identity.address.upload.pdf.button')}</Text>
      </TouchableOpacity>
    </View>
  );

  const renderUploadedFile = () => {
    if (!uploadedFile) return null;

    return (
      <View style={styles.uploadedFile}>
        {uploadedFile.type === 'image' ? (
          <Image
            source={{ uri: uploadedFile.uri }}
            style={styles.uploadedImage}
            contentFit="contain"
          />
        ) : (
          <View style={styles.uploadedPdf}>
            <MaterialCommunityIcons name="file-pdf-box" size={48} color={theme.colors.primary} />
            <Text style={styles.uploadedPdfName} numberOfLines={1}>
              {uploadedFile.name || t('identity.address.upload.pdf.title')}
            </Text>
          </View>
        )}
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => setUploadedFile(null)}
        >
          <MaterialCommunityIcons name="close" size={24} color={theme.colors.error} />
        </TouchableOpacity>
      </View>
    );
  };

  const handleSubmit = async () => {
    if (!uploadedFile) {
      setErrorMessage(t('identity.errors.incomplete'));
      setErrorDialogVisible(true);
      return;
    }

    setIsSubmitting(true);

    try {
      // Create FormData object
      const formData = new FormData();
      
      // Append verification type
      formData.append('verification_type', VerificationTypeEnum.AddressProof);

      // Append the file
      const fileUri = uploadedFile.uri;
      const fileName = uploadedFile.name || fileUri.split('/').pop() || 'address_proof';
      const fileType = uploadedFile.type === 'image' ? 'image/jpeg' : 'application/pdf'; 
      
      formData.append('document', {
          uri: fileUri,
          name: fileName,
          type: fileType,
      } as any);

      await submitVerificationMutation.mutateAsync(formData); 

      await refetchVerifications();
      setSuccessDialogVisible(true);

    } catch (error: any) {
      console.error('Error submitting address verification:', error);
      let specificMessage = t('identity.errors.upload'); // Default error
      if (error.response && error.response.data && error.response.data.error) {
        // Try to use specific error from backend if available
        const errorCode = error.response.data.error;
        const translationKey = `identity.errors.api.${errorCode.toLowerCase()}`;
        specificMessage = t(translationKey, { defaultValue: specificMessage });
      }
      setErrorMessage(specificMessage);
      setErrorDialogVisible(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSuccessConfirm = () => {
    setSuccessDialogVisible(false);
    // Replace current screen with Profile tab screen using Expo Router
    router.replace('/tabs/profile');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <View style={styles.content}>
          <View style={styles.section}>
            <Text style={styles.sectionDescription}>
              {t('identity.address.upload.description')}
            </Text>
            
            {uploadedFile ? renderUploadedFile() : renderUploadOptions()}
            
            <Text style={styles.sizeLimit}>
              {t('identity.address.upload.pdf.size')}
            </Text>
          </View>

          <View style={styles.submitSection}>
            <TouchableOpacity
              style={[
                styles.submitButton,
                (!uploadedFile || isSubmitting) && styles.submitButtonDisabled
              ]}
              onPress={handleSubmit}
              disabled={!uploadedFile || isSubmitting}
            >
              <Text style={styles.submitButtonText}>
                {isSubmitting ? t('common.loading') : t('identity.address.upload.button')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Success Dialog */}
      <CustomDialog
        visible={successDialogVisible}
        title={t('identity.address.upload.success.title')}
        message={t('identity.address.upload.success.message')}
        confirmText={t('common.ok')}
        onConfirm={handleSuccessConfirm}
        type="success"
      />

      {/* Error Dialog */}
      <CustomDialog
        visible={errorDialogVisible}
        title={t('common.error')}
        message={errorMessage}
        confirmText={t('common.ok')}
        onConfirm={() => setErrorDialogVisible(false)}
        type="error"
      />
    </SafeAreaView>
  );
} 