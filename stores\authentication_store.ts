import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface authenticationState {
    isAuthenticated: boolean;
    accessToken: string;
    refreshToken: string;
    setIsAuthorized: (isAuthorized: boolean) => void;
    setAccessToken: (accessToken: string) => void;
    setRefreshToken: (refreshToken: string) => void;
    logout: () => void;
}

export const authenticationStore = create<authenticationState>()(
    persist(
        (set) => ({
            isAuthenticated: false,
            accessToken: '',
            refreshToken: '',
            setIsAuthorized: (isAuthenticated: boolean) => set({ isAuthenticated: isAuthenticated }),
            setAccessToken: (accessToken: string) => set({ accessToken: accessToken }),
            setRefreshToken: (refreshToken: string) => set({ refreshToken: refreshToken }),
            logout: () => set({ isAuthenticated: false, accessToken: '', refreshToken: '' }),
        }),
        {
            name: 'auth-storage', // name of the item in the storage (must be unique)
            storage: createJSONStorage(() => AsyncStorage), // (optional) by default, 'localStorage' is used
        }
    )
);