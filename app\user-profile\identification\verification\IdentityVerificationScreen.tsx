import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Platform, TouchableOpacity, Modal, Alert, SafeAreaView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useTranslation } from 'react-i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import Ionicons from '@expo/vector-icons/Ionicons';
import { appStyleStore } from 'stores/app_style_store';
import { authenticationStore } from 'stores/authentication_store';
import { userProfileStore } from 'stores/user_store';
import { useRouter, useFocusEffect } from 'expo-router';
import Constants from 'expo-constants';
import { VerificationStatusEnum, VerificationTypeEnum } from 'types/enums';
import { VerificationPayload } from '@/api/api_config';
import { useFetchUserVerifications } from '@/api/user_services';
import { createTheme } from 'theme/index';
import type { MD3Theme as CustomTheme } from 'react-native-paper';

type IconName = React.ComponentProps<typeof Ionicons>['name'];
type MaterialIconName = React.ComponentProps<typeof MaterialCommunityIcons>['name'];

type RootStackParamList = {
  IdentityGuide: { documentType?: 'hkid' | 'mainland_travel_permit' | 'passport' | 'hkyouth' };
  AddressGuide: undefined;
};

const makeStyles = (theme: CustomTheme) => StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: Constants.statusBarHeight,
  },
  contentContainer: {
    paddingBottom: 20,
  },
  headerTitle: {
    fontSize: 26,
    fontWeight: 'bold',
    color: theme.system.text,
    paddingHorizontal: 16,
    paddingTop: 16, 
    paddingBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: theme.system.secondaryText,
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.system.text,
    paddingHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
  },
  listContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    marginHorizontal: 16,
    overflow: 'hidden',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: theme.system.border,
  },
  iconWrapper: {
    padding: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  listItemContent: {
    flex: 1,
  },
  listItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.system.text,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    fontSize: 14,
  },
});

const makeModalStyles = (theme: CustomTheme) => StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: '85%',
    maxWidth: 360,
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  header: {
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.system.text,
  },
  content: {
    marginBottom: 24,
  },
  message: {
    fontSize: 16,
    color: theme.system.secondaryText,
    textAlign: 'center',
    lineHeight: 22,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: theme.colors.surfaceVariant,
    marginRight: 8,
  },
  cancelText: {
    color: theme.colors.onSurfaceVariant,
    fontWeight: '600',
    fontSize: 16,
  },
  proceedButton: {
    backgroundColor: theme.colors.primary,
    marginLeft: 8,
  },
  proceedText: {
    color: theme.colors.onPrimary,
    fontWeight: '600',
    fontSize: 16,
  },
});

// Modal component for verification requirement
const VerificationRequirementModal = ({
  visible,
  onClose,
  onProceed,
  message,
  theme
}: {
  visible: boolean;
  onClose: () => void;
  onProceed: () => void;
  message: string;
  theme: CustomTheme;
}) => {
  const { t } = useTranslation();
  const modalStyles = makeModalStyles(theme);

  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={modalStyles.overlay}>
        <View style={modalStyles.container}>
          <View style={modalStyles.header}>
            <Text style={modalStyles.title}>{t('identity.verification.requirementsTitle')}</Text>
          </View>
          <View style={modalStyles.content}>
            <Text style={modalStyles.message}>{message}</Text>
          </View>
          <View style={modalStyles.actions}>
            <TouchableOpacity 
              style={[modalStyles.button, modalStyles.cancelButton]} 
              onPress={onClose}
            >
              <Text style={modalStyles.cancelText}>{t('common.cancel')}</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[modalStyles.button, modalStyles.proceedButton]} 
              onPress={onProceed}
            >
              <Text style={modalStyles.proceedText}>{t('common.continue')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const getStatusInfo = (status?: VerificationStatusEnum | null) => {
  if (!status) return {
    icon: 'alert-circle-outline' as const,
    color: '#FF9800',
    textKey: 'unverified'
  };

  switch (status) {
    case VerificationStatusEnum.Approved:
      return {
        icon: 'checkmark-circle-outline' as const,
        color: '#4CAF50',
        textKey: 'verified'
      };
    case VerificationStatusEnum.Pending:
      return {
        icon: 'time-outline' as const,
        color: '#757575',
        textKey: 'pending'
      };
    case VerificationStatusEnum.Rejected:
      return {
        icon: 'close-circle-outline' as const,
        color: '#FF3B30',
        textKey: 'rejected'
      };
    case VerificationStatusEnum.Unverified:
      return {
        icon: 'alert-circle-outline' as const,
        color: '#FF9800',
        textKey: 'unverified'
      };
    default:
      return {
        icon: 'alert-circle-outline' as const,
        color: '#FF9800',
        textKey: 'unverified'
      };
  }
};

const ListItem = ({
  icon,
  materialIcon,
  title,
  status,
  comment,
  onPress,
  disabled = false,
  theme
}: {
  icon?: IconName;
  materialIcon?: MaterialIconName;
  title: string;
  status?: VerificationStatusEnum;
  comment?: string | null;
  onPress?: () => void;
  disabled?: boolean;
  theme: CustomTheme;
}) => {
  const { t } = useTranslation();
  const statusInfo = getStatusInfo(status);

  return (
    <TouchableOpacity
      onPress={disabled ? undefined : onPress}
      style={makeStyles(theme).listItem}
      activeOpacity={disabled ? 1 : 0.7}
    >
      <View style={[makeStyles(theme).iconWrapper, { backgroundColor: theme.colors.primaryContainer }]}>
        {materialIcon ? (
          <MaterialCommunityIcons
            name={materialIcon}
            size={22}
            color={theme.colors.primary}
          />
        ) : (
          <Ionicons
            name={icon as IconName}
            size={22}
            color={theme.colors.primary}
          />
        )}
      </View>
      <View style={makeStyles(theme).listItemContent}>
        <Text style={makeStyles(theme).listItemTitle}>{title}</Text>
        <View style={makeStyles(theme).statusContainer}>
          <Ionicons
            name={statusInfo.icon}
            size={16}
            color={statusInfo.color}
            style={makeStyles(theme).statusIcon}
          />
          <Text style={[makeStyles(theme).statusText, { color: statusInfo.color }]}>
            {t(`identity.verification.statusLabels.${statusInfo.textKey}`)}
            {status === VerificationStatusEnum.Rejected && comment && ': ' + comment}
          </Text>
        </View>
      </View>
      {!disabled && (
        <Ionicons
          name="chevron-forward"
          size={20}
          color="#CCC"
        />
      )}
    </TouchableOpacity>
  );
};

export default function IdentityVerificationScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  const styles = makeStyles(theme);
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);
  const router = useRouter();
  const { data: verificationsData, refetch: refetchVerifications, isLoading: isLoadingVerifications } = useFetchUserVerifications();
  const [modalVisible, setModalVisible] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [intendedNavigation, setIntendedNavigation] = useState<'identity' | 'address'>('identity');
  const [selectedDocType, setSelectedDocType] = useState<VerificationTypeEnum>(VerificationTypeEnum.HkIDCard);

  useFocusEffect(
    React.useCallback(() => {
      if (isAuthenticated) {
        refetchVerifications();
      }
    }, [isAuthenticated, refetchVerifications])
  );

  const getVerificationByType = (docType: VerificationTypeEnum): VerificationPayload | undefined => {
    return verificationsData?.find(v => v.verification_type === docType.toString());
  };

  const hasAnyIdentityDocumentVerified = (): boolean => {
    const hkidVerification = getVerificationByType(VerificationTypeEnum.HkIDCard);
    const passportVerification = getVerificationByType(VerificationTypeEnum.Passport);
    const mainlandTravelPermitVerification = getVerificationByType(VerificationTypeEnum.MainlandTravelPermit);
    const hkyouthVerification = getVerificationByType(VerificationTypeEnum.HkYouthPlus);

    return (
      hkidVerification?.status === VerificationStatusEnum.Approved || 
      passportVerification?.status === VerificationStatusEnum.Approved || 
      mainlandTravelPermitVerification?.status === VerificationStatusEnum.Approved || 
      hkyouthVerification?.status === VerificationStatusEnum.Approved
    );
  };

  const handleNavigateToIdentityGuide = (documentType: VerificationTypeEnum = VerificationTypeEnum.HkIDCard) => {
    setIntendedNavigation('identity');
    setSelectedDocType(documentType);
    router.push({ pathname: '../IdentityGuideScreen', params: { documentType: documentType.toString() } });
  };

  const handleNavigateToAddressGuide = () => {
    if (!hasAnyIdentityDocumentVerified()) {
      setIntendedNavigation('address');
      setModalMessage(t('identity.address.requiresIdentityVerification'));
      setModalVisible(true);
    } else {
      router.push('../AddressGuideScreen');
    }
  };

  const handleCloseModal = () => {
    setModalVisible(false);
  };

  const handleProceedWithIdentityVerification = () => {
    setModalVisible(false);
    router.push({ pathname: '../IdentityGuideScreen', params: { documentType: VerificationTypeEnum.HkIDCard.toString() } });
  };

  const getDocStatus = (docType: VerificationTypeEnum) => getVerificationByType(docType)?.status as VerificationStatusEnum | undefined;
  const getDocComment = (docType: VerificationTypeEnum) => getVerificationByType(docType)?.admin_notes;

  const isHkidDisabled = 
    getDocStatus(VerificationTypeEnum.HkIDCard) === VerificationStatusEnum.Approved || 
    getDocStatus(VerificationTypeEnum.HkIDCard) === VerificationStatusEnum.Pending;

  const isMainlandPermitDisabled = 
    getDocStatus(VerificationTypeEnum.MainlandTravelPermit) === VerificationStatusEnum.Approved || 
    getDocStatus(VerificationTypeEnum.MainlandTravelPermit) === VerificationStatusEnum.Pending;
  
  const isPassportDisabled = 
    getDocStatus(VerificationTypeEnum.Passport) === VerificationStatusEnum.Approved || 
    getDocStatus(VerificationTypeEnum.Passport) === VerificationStatusEnum.Pending;

  const isHkyouthDisabled = 
    getDocStatus(VerificationTypeEnum.HkYouthPlus) === VerificationStatusEnum.Approved || 
    getDocStatus(VerificationTypeEnum.HkYouthPlus) === VerificationStatusEnum.Pending;
  
  const isStudentDisabled = 
    getDocStatus(VerificationTypeEnum.StudentID) === VerificationStatusEnum.Approved || 
    getDocStatus(VerificationTypeEnum.StudentID) === VerificationStatusEnum.Pending;

  const isAddressDisabled = 
    getDocStatus(VerificationTypeEnum.AddressProof) === VerificationStatusEnum.Approved || 
    getDocStatus(VerificationTypeEnum.AddressProof) === VerificationStatusEnum.Pending;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.system.background }]}>
      <ScrollView contentContainerStyle={styles.contentContainer}>
        <StatusBar style={Platform.OS === 'ios' ? 'dark' : 'light'} backgroundColor={theme.system.background} />
        <Text style={styles.headerTitle}>{t('identity.verification.title')}</Text>
        <Text style={styles.headerSubtitle}>{t('identity.verification.subtitle')}</Text>

        <Text style={styles.sectionTitle}>{t('identity.verification.identityDocuments')}</Text>
        <View style={styles.listContainer}>
          <ListItem
            icon="card-outline"
            title={t('identity.verification.documents.hkid')}
            status={getDocStatus(VerificationTypeEnum.HkIDCard)}
            comment={getDocComment(VerificationTypeEnum.HkIDCard)}
            onPress={() => handleNavigateToIdentityGuide(VerificationTypeEnum.HkIDCard)}
            disabled={isHkidDisabled}
            theme={theme}
          />
          <ListItem
            icon="earth-outline"
            title={t('identity.verification.documents.mainlandTravelPermit')}
            status={getDocStatus(VerificationTypeEnum.MainlandTravelPermit)}
            comment={getDocComment(VerificationTypeEnum.MainlandTravelPermit)}
            onPress={() => handleNavigateToIdentityGuide(VerificationTypeEnum.MainlandTravelPermit)}
            disabled={isMainlandPermitDisabled}
            theme={theme}
          />
          <ListItem
            icon="person-circle-outline"
            title={t('identity.verification.documents.passport')}
            status={getDocStatus(VerificationTypeEnum.Passport)}
            comment={getDocComment(VerificationTypeEnum.Passport)}
            onPress={() => handleNavigateToIdentityGuide(VerificationTypeEnum.Passport)}
            disabled={isPassportDisabled}
            theme={theme}
          />
           <ListItem
            icon="ribbon-outline"
            title={t('identity.verification.documents.hkYouthPlus')}
            status={getDocStatus(VerificationTypeEnum.HkYouthPlus)}
            comment={getDocComment(VerificationTypeEnum.HkYouthPlus)}
            onPress={() => handleNavigateToIdentityGuide(VerificationTypeEnum.HkYouthPlus)}
            disabled={isHkyouthDisabled}
            theme={theme}
          />
        </View>

        <Text style={styles.sectionTitle}>{t('identity.verification.otherDocuments')}</Text>
        <View style={styles.listContainer}>
          <ListItem
            icon="home-outline"
            title={t('identity.verification.documents.addressProof')}
            status={getDocStatus(VerificationTypeEnum.AddressProof)}
            comment={getDocComment(VerificationTypeEnum.AddressProof)}
            onPress={handleNavigateToAddressGuide}
            disabled={isAddressDisabled}
            theme={theme}
          />
          {/* Add more ListItems for other document types if needed */}
        </View>
      </ScrollView>
      <VerificationRequirementModal
        visible={modalVisible}
        onClose={handleCloseModal}
        onProceed={handleProceedWithIdentityVerification}
        message={modalMessage}
        theme={theme}
      />
    </SafeAreaView>
  );
}