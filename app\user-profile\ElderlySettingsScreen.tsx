import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform
} from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Card } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { Stack } from 'expo-router';
import { appStyleStore } from 'stores/app_style_store';
import type { MD3Theme as CustomTheme } from 'react-native-paper';
import { createTheme } from 'theme/index';
import { format, parseISO } from 'date-fns';

const mockEvent = {
  id: 'preview',
  title: '2024年健康生活講座：活出精彩人生',
  description: '本次講座特別邀請資深醫學專家王醫師，為大家分享健康生活指南。內容包括：均衡飲食建議、適合各年齡層的運動方式、疾病預防與管理、心理健康維護等重要議題。現場還有互動問答環節，歡迎大家踴躍提問。',
  date: new Date().toISOString(),
  location: '香港服務中心（九龍灣社區活動中心三樓）',
  image: 'https://picsum.photos/600/418',
  price: 0,
  attendees: 120,
};

const modeConfig = {
  normal: {
    fontSize: 18,
    icon: 'account',
    color: '#4A5568',
  },
  senior: {
    fontSize: 24,
    icon: 'account-heart',
    color: '#2B6CB0',
  }
} as const;

const ElderlySettingsScreen = () => {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  const [selectedMode, setSelectedMode] = useState<keyof typeof modeConfig>('senior');
  const styles = getStyles(theme, selectedMode);

  const renderPreviewCard = () => (
    <Card style={styles.previewCard} mode="elevated">
      <View style={styles.cardWrapper}>
        <View style={styles.cardImageContainer}>
          <Card.Cover 
            source={{ uri: mockEvent.image }} 
            style={styles.cardImage} 
          />
        </View>
        <Card.Content style={styles.cardContent}>
          <View style={styles.dateLocationContainer}>
            <View style={styles.iconTextContainer}>
              <MaterialCommunityIcons 
                name="calendar" 
                size={selectedMode === 'senior' ? 20 : 16} 
                color="#666666" 
              />
              <Text 
                style={styles.dateText}
                numberOfLines={1}
              >
                {format(parseISO(mockEvent.date), 'yyyy-MM-dd')}
              </Text>
            </View>
            <View style={styles.locationContainer}>
              <MaterialCommunityIcons 
                name="map-marker" 
                size={selectedMode === 'senior' ? 20 : 16} 
                color="#666666" 
              />
              <Text 
                style={styles.locationText}
                numberOfLines={1}
              >
                {mockEvent.location}
              </Text>
            </View>
          </View>
          <Text 
            style={styles.cardTitle}
            numberOfLines={2}
          >
            {mockEvent.title}
          </Text>
          <Text 
            style={styles.cardDescription}
            numberOfLines={2}
          >
            {mockEvent.description}
          </Text>
          <View style={styles.cardFooter}>
            <View style={[styles.priceContainer, { backgroundColor: '#FFF3E6' }]}>
              <Text 
                style={styles.priceText}
                numberOfLines={1}
              >
                {t('elderlySettings.preview.event.free')}
              </Text>
            </View>
            <View style={styles.attendeesContainer}>
              <MaterialCommunityIcons 
                name="account-group" 
                size={selectedMode === 'senior' ? 20 : 16} 
                color="#666666" 
              />
              <Text 
                style={styles.attendeesText}
                numberOfLines={1}
              >
                {t('elderlySettings.preview.event.attendees', { count: mockEvent.attendees })}
              </Text>
            </View>
          </View>
        </Card.Content>
      </View>
    </Card>
  );

  return (
    <>
      <Stack.Screen options={{ title: t('elderlySettings.title') }} />
      <ScrollView contentContainerStyle={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>{t('elderlySettings.selectMode')}</Text>
        </View>

        <View style={styles.modeContainer}>
          {(Object.entries(modeConfig) as [keyof typeof modeConfig, typeof modeConfig[keyof typeof modeConfig]][]).map(([mode, config]) => (
            <TouchableOpacity
              key={mode}
              activeOpacity={0.7}
              onPress={() => setSelectedMode(mode)}
              style={[
                styles.modeCard,
                selectedMode === mode && { borderColor: config.color }
              ]}
            >
              <View style={styles.cardHeader}>
                <MaterialCommunityIcons
                  name={config.icon as any}
                  size={36}
                  color={selectedMode === mode ? config.color : '#718096'}
                />
                <Text style={[
                  styles.modeLabel,
                  { color: selectedMode === mode ? config.color : '#718096' }
                ]}>
                  {t(`elderlySettings.modes.${mode}.label`)}
                </Text>
                <Text style={styles.modeDesc}>
                  {t(`elderlySettings.modes.${mode}.desc`)}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.previewBox}>
          <Text style={styles.previewTitle}>
            {t('elderlySettings.preview.title')}
            <Text style={styles.sizeLabel}>
              {t('elderlySettings.preview.fontSize', { size: modeConfig[selectedMode].fontSize })}
            </Text>
          </Text>
          {renderPreviewCard()}
        </View>
      </ScrollView>
    </>
  );
};

const getStyles = (theme: CustomTheme, selectedMode: keyof typeof modeConfig) => StyleSheet.create({
  container: {
    flexGrow: 1,
    backgroundColor: theme.colors.background,
    padding: 24
  },
  header: {
    marginBottom: 32,
    paddingHorizontal: 8
  },
  title: {
    fontSize: 24,
    color: theme.system.text,
    fontWeight: '600'
  },
  modeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32
  },
  modeCard: {
    width: '48%',
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 16,
    borderWidth: 2,
    borderColor: theme.system.border,
  },
  cardHeader: {
    alignItems: 'center',
  },
  modeLabel: {
    fontSize: 20,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 8,
  },
  modeDesc: {
    fontSize: 14,
    color: theme.system.secondaryText,
    textAlign: 'center'
  },
  previewBox: {
    marginTop: 32,
  },
  previewTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 20,
    color: theme.system.text,
  },
  sizeLabel: {
    fontSize: 16,
    color: theme.system.secondaryText,
  },
  previewCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    borderWidth: Platform.OS === 'android' ? 0 : 1,
    borderColor: theme.system.border,
  },
  cardWrapper: {
    borderRadius: 12,
    overflow: 'hidden', 
  },
  cardImageContainer: {
    height: 180,
    backgroundColor: theme.colors.surfaceVariant,
  },
  cardImage: {
    height: '100%',
    width: '100%',
  },
  cardContent: {
    padding: 16,
  },
  dateLocationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  iconTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8, 
  },
  dateText: {
    marginLeft: 6,
    color: theme.system.secondaryText,
    fontSize: selectedMode === 'senior' ? 18 : 14,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 1,
  },
  locationText: {
    marginLeft: 6,
    color: theme.system.secondaryText,
    fontSize: selectedMode === 'senior' ? 18 : 14,
    flexShrink: 1,
  },
  cardTitle: {
    fontSize: selectedMode === 'senior' ? 22 : 18,
    fontWeight: '600',
    color: theme.system.text,
    marginBottom: 8,
    lineHeight: selectedMode === 'senior' ? 30 : 24,
  },
  cardDescription: {
    fontSize: selectedMode === 'senior' ? 18 : 14,
    color: theme.system.secondaryText,
    marginBottom: 12,
    lineHeight: selectedMode === 'senior' ? 26 : 20,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  priceContainer: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    backgroundColor: theme.colors.tertiaryContainer,
  },
  priceText: {
    fontSize: selectedMode === 'senior' ? 18 : 14,
    color: theme.colors.onTertiaryContainer,
    fontWeight: '600',
  },
  attendeesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  attendeesText: {
    marginLeft: 6,
    fontSize: selectedMode === 'senior' ? 18 : 14,
    color: theme.system.secondaryText,
  },
});

export default ElderlySettingsScreen;
