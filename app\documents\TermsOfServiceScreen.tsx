import React from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import { Stack } from 'expo-router';

interface Section {
  title: string;
  content: string;
}

export function TermsOfServiceScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme);
  const styles = getThemedStyles(theme);

  const sections = t('termsOfService.sections', { returnObjects: true }) as Section[];

  return (
    <View style={styles.container}>
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: t('profile.items.termsOfService.title'),
        }}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {/* Header Section */}
          <View style={styles.headerSection}>
            <Text style={styles.lastUpdated}>
              {t('termsOfService.lastUpdated')}
            </Text>
            <Text style={styles.introduction}>
              {t('termsOfService.introduction')}
            </Text>
          </View>

          {/* Content Sections */}
          {sections.map((section: Section, index: number) => (
            <View key={index} style={styles.section}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionNumberContainer}>
                  <Text style={styles.sectionNumber}>
                    {section.title.split('.')[0]}
                  </Text>
                </View>
                <Text style={styles.sectionTitle}>
                  {section.title.split('.')[1]}
                </Text>
              </View>
              <Text style={styles.sectionContent}>
                {section.content}
              </Text>
            </View>
          ))}

          {/* Footer Section */}
          <View style={styles.footerSection}>
            <View style={styles.footerDivider} />
            <Text style={styles.footer}>
              {t('termsOfService.footer')}
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const getThemedStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingBottom: 32,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  lastUpdated: {
    fontSize: 14,
    marginBottom: 8,
    color: theme.system.secondaryText,
  },
  introduction: {
    fontSize: 15,
    lineHeight: 22,
    textAlign: 'center',
    paddingHorizontal: 20,
    color: theme.system.text,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionNumberContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: `${theme.colors.primary}1A`,
  },
  sectionNumber: {
    fontSize: 15,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '600',
    flex: 1,
    color: theme.system.text,
  },
  sectionContent: {
    fontSize: 15,
    lineHeight: 24,
    paddingLeft: 40,
    color: theme.system.text,
  },
  footerSection: {
    marginTop: 32,
    alignItems: 'center',
  },
  footerDivider: {
    width: 32,
    height: 3,
    borderRadius: 1.5,
    marginBottom: 16,
    backgroundColor: `${theme.colors.primary}33`,
  },
  footer: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 32,
    lineHeight: 20,
    color: theme.system.secondaryText,
  },
});

export default TermsOfServiceScreen;
