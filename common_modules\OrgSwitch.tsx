import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
  Platform,
  Image,
  Modal,
  TouchableWithoutFeedback,
  ScrollView,
} from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import { createTheme } from 'theme/index';
import type { Organization } from 'types/organization';

interface Props {
  visible: boolean;
  onClose: (selectedOrg?: Organization) => void;
  organizations: Organization[];
  currentOrganization: Organization | null;
  onSelectOrganization?: (organization: Organization) => void;
}


export const OrgSwitch: React.FC<Props> = ({
  visible,
  onClose,
  organizations,
  currentOrganization,
  onSelectOrganization,
}) => {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme);
  const [selectedOrgId, setSelectedOrgId] = React.useState<string>(
    currentOrganization?.id || ''
  );
  const [animation] = React.useState(new Animated.Value(0));

  React.useEffect(() => {
    if (visible && currentOrganization && currentOrganization.id) {
      setSelectedOrgId(currentOrganization.id);
    }
  }, [visible, currentOrganization]);

  React.useEffect(() => {
    if (visible) {
      // Enter animation
      Animated.timing(animation, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true,
      }).start();
    } else {
      // Exit animation
      Animated.timing(animation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  const translateY = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [Dimensions.get('window').height, 0],
  });

  const handleClose = (organization?: Organization) => {
    // Run exit animations first
    Animated.timing(animation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      // Call onClose after animations complete, passing the organization
      onClose(organization);
    });
  };

  const handleConfirm = () => {
    const selectedOrg = organizations.find(org => org.id === selectedOrgId);
    // Pass selectedOrg (if it's different) to handleClose, which will then pass it to the main onClose callback
    if (selectedOrg && selectedOrg.id !== currentOrganization?.id) {
      // Call onSelectOrganization with the selected organization
      if (onSelectOrganization && selectedOrg) {
        onSelectOrganization(selectedOrg);
      }
      handleClose(selectedOrg);
    } else {
      handleClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={() => handleClose()}
    >
      <TouchableWithoutFeedback onPress={() => handleClose()}>
        <Animated.View style={[styles.overlay, { opacity: animation }]}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.content,
                {
                  transform: [{ translateY }],
                }
              ]}
            >
              <View style={styles.handle} />
              <Text style={styles.title}>{t('dashboard.selectOrganization')}</Text>
              <ScrollView style={styles.optionsContainer} contentContainerStyle={styles.optionsContent}>
                {organizations.map((org) => {
                  const orgTheme = createTheme(org.theme);
                  return (
                    <TouchableOpacity
                      key={org.id}
                      style={[
                        styles.option,
                        selectedOrgId === org.id && {
                          backgroundColor: orgTheme.colors.primaryContainer
                        }
                      ]}
                      onPress={() => org.id && setSelectedOrgId(org.id)}
                    >
                      <View style={styles.orgContent}>
                        <Image
                          source={org.logo}
                          style={styles.orgLogo}
                          resizeMode="contain"
                        />
                        <Text style={[
                          styles.optionText,
                          selectedOrgId === org.id && [
                            styles.selectedText,
                            { color: orgTheme.colors.primary }
                          ]
                        ]}>
                          {org.name}
                        </Text>
                      </View>
                      {selectedOrgId === org.id && (
                        <MaterialCommunityIcons
                          name="check"
                          size={24}
                          color={orgTheme.colors.primary}
                        />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
              <TouchableOpacity
                style={[styles.confirmButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleConfirm}
              >
                <Text style={styles.confirmButtonText}>{t('dashboard.switchOrganization')}</Text>
              </TouchableOpacity>
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  content: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    paddingTop: 12,
    paddingBottom: Platform.OS === 'ios' ? 34 : 24,
  },
  handle: {
    width: 36,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
    marginLeft: 12,
  },
  optionsContainer: {
    marginBottom: 24,
    maxHeight: Dimensions.get('window').height * 0.5,
  },
  optionsContent: {
    paddingHorizontal: 12,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 56,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  optionText: {
    fontSize: 16,
    color: '#333333',
    flex: 1,
  },
  selectedText: {
    fontWeight: '600',
  },
  confirmButton: {
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 12,
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  orgContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  orgLogo: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12,
  },
}); 