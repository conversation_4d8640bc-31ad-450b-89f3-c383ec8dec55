import { VerificationTypeEnum } from 'types/enums';
import { FormData } from './IdentityInfoScreen';

/**
 * Verification Parameter Mapper
 * 
 * This helper manages the mapping between frontend form field names and backend API parameter names
 * for all verification types. It also handles data format conversions (e.g., date formats).
 */

// Type definitions for mapped parameters
export interface MappedVerificationParams {
  [key: string]: string | boolean;
}

// Date format conversion utility
const convertDateFormat = (dateString: string, fromFormat: 'DD-MM-YYYY' | 'YYYY-MM-DD', toFormat: 'DD-MM-YYYY' | 'YYYY-MM-DD'): string => {
  if (!dateString) return '';
  
  if (fromFormat === 'DD-MM-YYYY' && toFormat === 'YYYY-MM-DD') {
    const [day, month, year] = dateString.split('-');
    return `${year}-${month}-${day}`;
  } else if (fromFormat === 'YYYY-MM-DD' && toFormat === 'DD-MM-YYYY') {
    const [year, month, day] = dateString.split('-');
    return `${day}-${month}-${year}`;
  }
  
  return dateString; // Return as-is if no conversion needed
};

// Address construction utility
const constructFullAddress = (formData: FormData): string => {
  const addressParts = [
    formData.addressUnit,
    formData.addressFloor,
    formData.addressBuilding,
    formData.addressStreet,
    formData.addressDistrict,
    formData.addressRegion
  ];
  return addressParts.filter(part => part && String(part).trim() !== '').join(', ');
};

/**
 * Maps frontend form data to backend API parameters for HKID verification
 * Backend expects: chinese_name, chinese_code, english_name, sex, dob, id_number, is_hk_permanent_resident
 */
const mapHkIdCardParams = (formData: FormData): MappedVerificationParams => {
  const params: MappedVerificationParams = {};
  
  if (formData.hkidEnglishName) {
    params['english_name'] = formData.hkidEnglishName;
  }
  
  if (formData.hkidChineseName) {
    params['chinese_name'] = formData.hkidChineseName;
  }
  
  if (formData.hkidDob) {
    // Convert DD-MM-YYYY to YYYY-MM-DD
    params['dob'] = convertDateFormat(formData.hkidDob, 'DD-MM-YYYY', 'YYYY-MM-DD');
  }
  
  if (formData.hkidGender) {
    params['sex'] = formData.hkidGender;
  }
  
  if (formData.hkidNumber) {
    params['id_number'] = formData.hkidNumber;
  }
  
  if (formData.hkidPermanentResident !== undefined) {
    params['is_hk_permanent_resident'] = formData.hkidPermanentResident;
  }
  
  if (formData.hkidChineseCommercialCode) {
    params['chinese_code'] = formData.hkidChineseCommercialCode;
  }
  
  return params;
};

/**
 * Maps frontend form data to backend API parameters for Mainland China ID verification
 * Backend expects: chinese_name, sex, date_of_birth, mainland_id_number, valid_until
 */
const mapMainlandChinaIdParams = (formData: FormData): MappedVerificationParams => {
  const params: MappedVerificationParams = {};
  
  if (formData.mainlandIdChineseName) {
    params['chinese_name'] = formData.mainlandIdChineseName;
  }
  
  if (formData.mainlandIdGender) {
    params['sex'] = formData.mainlandIdGender;
  }
  
  if (formData.mainlandIdDob) {
    params['date_of_birth'] = convertDateFormat(formData.mainlandIdDob, 'DD-MM-YYYY', 'YYYY-MM-DD');
  }
  
  if (formData.mainlandIdNumber) {
    params['mainland_id_number'] = formData.mainlandIdNumber;
  }
  
  if (formData.mainlandIdExpiryDate) {
    params['valid_until'] = convertDateFormat(formData.mainlandIdExpiryDate, 'DD-MM-YYYY', 'YYYY-MM-DD');
  }
  
  return params;
};

/**
 * Maps frontend form data to backend API parameters for Mainland Travel Permit verification
 * Backend expects: mainland_travel_permit_number, mainland_travel_permit_issue_date, mainland_travel_permit_expiry_date
 */
const mapMainlandTravelPermitParams = (formData: FormData): MappedVerificationParams => {
  const params: MappedVerificationParams = {};
  
  if (formData.mainlandTravelPermitNumber) {
    params['mainland_travel_permit_number'] = formData.mainlandTravelPermitNumber;
  }
  
  if (formData.mainlandTravelPermitIssueDate) {
    params['mainland_travel_permit_issue_date'] = convertDateFormat(formData.mainlandTravelPermitIssueDate, 'DD-MM-YYYY', 'YYYY-MM-DD');
  }
  
  if (formData.mainlandTravelPermitExpiryDate) {
    params['mainland_travel_permit_expiry_date'] = convertDateFormat(formData.mainlandTravelPermitExpiryDate, 'DD-MM-YYYY', 'YYYY-MM-DD');
  }
  
  return params;
};

/**
 * Maps frontend form data to backend API parameters for Passport verification
 * Backend expects: passport_number, issuing_country, passport_issue_date, passport_expiry_date
 */
const mapPassportParams = (formData: FormData): MappedVerificationParams => {
  const params: MappedVerificationParams = {};
  
  if (formData.passportNumber) {
    params['passport_number'] = formData.passportNumber;
  }
  
  if (formData.passportIssuingCountry) {
    params['issuing_country'] = formData.passportIssuingCountry;
  }
  
  if (formData.passportIssueDate) {
    params['passport_issue_date'] = convertDateFormat(formData.passportIssueDate, 'DD-MM-YYYY', 'YYYY-MM-DD');
  }
  
  if (formData.passportExpiryDate) {
    params['passport_expiry_date'] = convertDateFormat(formData.passportExpiryDate, 'DD-MM-YYYY', 'YYYY-MM-DD');
  }
  
  return params;
};

/**
 * Maps frontend form data to backend API parameters for HK Youth Plus verification
 * Backend expects: member_number
 */
const mapHkYouthPlusParams = (formData: FormData): MappedVerificationParams => {
  const params: MappedVerificationParams = {};
  
  if (formData.hkYouthMembershipNumber) {
    params['member_number'] = formData.hkYouthMembershipNumber;
  }
  
  return params;
};

/**
 * Maps frontend form data to backend API parameters for Address Proof verification
 * Backend expects: full_address
 */
const mapAddressProofParams = (formData: FormData): MappedVerificationParams => {
  const params: MappedVerificationParams = {};
  
  // Construct full address from individual address fields
  const fullAddress = constructFullAddress(formData);
  if (fullAddress) {
    params['full_address'] = fullAddress;
  }
  
  return params;
};

/**
 * Maps frontend form data to backend API parameters for Student ID verification
 * Backend expects: school_name, grade, expiry_date
 */
const mapStudentIdParams = (formData: FormData): MappedVerificationParams => {
  const params: MappedVerificationParams = {};
  
  if (formData.studentIdSchoolName) {
    params['school_name'] = formData.studentIdSchoolName;
  }
  
  if (formData.studentIdGrade) {
    params['grade'] = formData.studentIdGrade;
  }
  
  if (formData.studentIdExpiryDate) {
    params['expiry_date'] = convertDateFormat(formData.studentIdExpiryDate, 'DD-MM-YYYY', 'YYYY-MM-DD');
  }
  
  return params;
};

/**
 * Maps frontend form data to backend API parameters for Home Visit verification
 * Backend expects: home_visit_notes (optional)
 */
const mapHomeVisitParams = (formData: FormData): MappedVerificationParams => {
  const params: MappedVerificationParams = {};
  
  // Home visit typically doesn't require additional parameters beyond applicant info
  // Notes can be added by admin during review process
  // For now, we don't collect notes from the frontend form
  
  return params;
};

/**
 * Main mapping function that routes to the appropriate mapper based on verification type
 */
export const mapVerificationParams = (
  verificationType: VerificationTypeEnum,
  formData: FormData
): MappedVerificationParams => {
  switch (verificationType) {
    case VerificationTypeEnum.HkIDCard:
      return mapHkIdCardParams(formData);
      
    case VerificationTypeEnum.MainlandChinaIDCard:
      return mapMainlandChinaIdParams(formData);
      
    case VerificationTypeEnum.MainlandTravelPermit:
      return mapMainlandTravelPermitParams(formData);
      
    case VerificationTypeEnum.Passport:
      return mapPassportParams(formData);
      
    case VerificationTypeEnum.HkYouthPlus:
      return mapHkYouthPlusParams(formData);
      
    case VerificationTypeEnum.AddressProof:
      return mapAddressProofParams(formData);
      
    case VerificationTypeEnum.StudentID:
      return mapStudentIdParams(formData);
      
    case VerificationTypeEnum.HomeVisit:
      return mapHomeVisitParams(formData);
      
    default:
      console.warn(`Unknown verification type: ${verificationType}`);
      return {};
  }
};

/**
 * Utility function to append mapped parameters to FormData object
 */
export const appendMappedParamsToFormData = (
  formData: globalThis.FormData,
  verificationType: VerificationTypeEnum,
  frontendFormData: FormData
): void => {
  // Add verification type
  formData.append('verification_type', verificationType);
  
  // Get mapped parameters
  const mappedParams = mapVerificationParams(verificationType, frontendFormData);
  
  // Append each parameter to FormData
  Object.entries(mappedParams).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      formData.append(key, String(value));
    }
  });
};

/**
 * Get file parameter names for different verification types
 */
export const getFileParameterNames = (verificationType: VerificationTypeEnum): {
  document: string;
  document2?: string;
} => {
  const dualImageTypes = [
    VerificationTypeEnum.HkIDCard,
    VerificationTypeEnum.MainlandChinaIDCard,
    VerificationTypeEnum.MainlandTravelPermit
  ];
  
  return {
    document: 'document',
    document2: dualImageTypes.includes(verificationType) ? 'document_2' : undefined
  };
};

/**
 * Check if verification type requires a document file
 */
export const requiresDocument = (verificationType: VerificationTypeEnum): boolean => {
  // Home visit doesn't require document upload
  return verificationType !== VerificationTypeEnum.HomeVisit;
};

/**
 * Check if verification type requires a second document file
 */
export const requiresSecondDocument = (verificationType: VerificationTypeEnum): boolean => {
  const dualImageTypes = [
    VerificationTypeEnum.HkIDCard,
    VerificationTypeEnum.MainlandChinaIDCard,
    VerificationTypeEnum.MainlandTravelPermit
  ];
  
  return dualImageTypes.includes(verificationType);
};

/**
 * Get single image submission types (types that only need front image)
 */
export const getSingleImageSubmissionTypes = (): VerificationTypeEnum[] => {
  return [
    VerificationTypeEnum.Passport,
    VerificationTypeEnum.HkYouthPlus,
    VerificationTypeEnum.AddressProof,
    VerificationTypeEnum.StudentID,
  ];
};

export default {
  mapVerificationParams,
  appendMappedParamsToFormData,
  getFileParameterNames,
  requiresDocument,
  requiresSecondDocument,
  getSingleImageSubmissionTypes,
  convertDateFormat,
  constructFullAddress
}; 