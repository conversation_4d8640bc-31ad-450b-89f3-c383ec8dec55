import React, { useState, useEffect, useLayoutEffect, useCallback, useRef } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    Image,
    TouchableOpacity,
    Platform,
    Share,
    Dimensions,
    Linking,
    ActivityIndicator,
    Alert,
    Modal,
    StatusBar,
    FlatList,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter, useLocalSearchParams, Stack, useNavigation } from 'expo-router';
import { parseISO, format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { ErrorView } from '@/common_modules/ErrorView';
import { appStyleStore } from 'stores/app_style_store';
import { createTheme } from 'theme/index';
import { MediaItemPayload as MediaItem, MEDIA_BASE_URL } from '@/api/api_config';
import { useFetchPostDetails } from '@/api/posts_services';
import { RichTextDescription } from '@/common_modules/RichTextDescription';
import { generatePostShareContent } from 'utils/shareUtils';
import ImageCarousel from '@/app/explore/events/components/ImageCarousel';
import ImageViewer from '@/app/explore/events/components/ImageViewer';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Removed getLocale function as we now use unified date format

interface AttachmentItemProps {
    attachment: {
        type: string;
        url: string;
        name: string;
        file_path: string;
        file_size?: number;
    };
    theme: any;
    styles: any;
    t: (key: string) => string;
    openVideoModal: (url: string) => void;
    isLast?: boolean;
}

const AttachmentItem = ({ attachment, theme, styles, t, openVideoModal, isLast }: AttachmentItemProps) => {
    const getFileIcon = (type: string) => {
        switch (type) {
            case 'pdf': return 'file-pdf-box';
            case 'txt':
            case 'doc':
            case 'docx': return 'file-word';
            case "csv":
            case 'xls':
            case 'xlsx': return 'file-excel';
            case 'zip':
            case '7z': return 'folder-zip';
            case 'video': return 'video';
            case 'ppt':
            case 'pptx': return 'file-powerpoint';
            case 'png':
            case 'jpg':
            case 'jpeg':
            case 'gif':
            case 'heic':
            case 'heif': return 'file-image';
            default: return 'file-document-outline';
        }
    };

    const getFileIconColor = (type: string) => {
        switch (type) {
            case 'pdf': return '#F40F02';
            case 'txt':
            case 'doc':
            case 'docx': return '#4F8CC9';
            case 'csv':
            case 'xls':
            case 'xlsx': return '#1D6F42';
            case 'zip':
            case '7z': return '#FFA940';
            case 'video': return '#FF5252';
            case 'ppt':
            case 'pptx': return '#D24726';
            case 'png':
            case 'jpg':
            case 'jpeg':
            case 'gif':
            case 'heic':
            case 'heif': return '#4CAF50';
            default: return theme.system.secondaryText;
        }
    };

    const formatFileSize = (bytes: number | undefined): string => {
        if (!bytes || bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
    };

    const handlePreview = async () => {
        const url = new URL(attachment.file_path, MEDIA_BASE_URL);
        const attachmentUrl = new URL(url.pathname, MEDIA_BASE_URL).toString();
        if (attachment.type === 'video') {
            openVideoModal(attachmentUrl);
        } else {
            try {
                await Linking.openURL(attachmentUrl);
            } catch (error) {
                Alert.alert(t('common.error'), t('posts.detail.previewError'), [{ text: t('common.ok') }]);
            }
        }
    };

    const fileType = attachment.type === 'video' ? 'video' : getFileType(attachment.file_path || attachment.name);

    return (
        <TouchableOpacity
            onPress={handlePreview}
            style={[styles.attachmentListItem, isLast ? styles.attachmentLastItem : undefined]}
            activeOpacity={0.7}
        >
            <View style={styles.attachmentIconContainer}>
                <MaterialCommunityIcons
                    name={getFileIcon(fileType)}
                    size={28}
                    color={getFileIconColor(fileType)}
                />
            </View>
            <View style={styles.attachmentTextContainer}>
                <Text style={styles.attachmentTitle} numberOfLines={1}>
                    {attachment.name}
                </Text>
                <Text style={styles.attachmentSubtitle}>
                    {formatFileSize(attachment.file_size)}
                </Text>
            </View>
            <View style={styles.attachmentArrowContainer}>
                <MaterialCommunityIcons
                    name="open-in-new"
                    size={18}
                    color={theme.system.secondaryText}
                />
            </View>
        </TouchableOpacity>
    );
};

const formatPostDate = (dateString: string | undefined): string => {
    if (!dateString) return '';
    try {
        const timeZone = 'Asia/Hong_Kong';
        const parsedDate = parseISO(dateString);
        if (isNaN(parsedDate.getTime())) {
            return '';
        }
        const zonedDate = toZonedTime(parsedDate, timeZone);
        return format(zonedDate, 'yyyy-MM-dd HH:mm');
    } catch (error) {
        console.warn('Error formatting date:', error);
        return '';
    }
};

const isImageFile = (fileType: string): boolean => {
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'heic', 'heif'];
    return imageTypes.includes(fileType.toLowerCase());
};

const getFileType = (fileName: string | undefined): string => {
    if (!fileName) return 'unknown';
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    if (['mp4', 'mov', 'avi', 'mkv', 'webm'].includes(extension)) {
        return 'video';
    }
    return extension;
};

export const PostsDetailScreen: React.FC = () => {
    const { t, i18n } = useTranslation();
    const storeTheme = appStyleStore(state => state.theme);
    const activeTheme = storeTheme || createTheme('red');
    const router = useRouter();
    const params = useLocalSearchParams<{ postsId: string, postsOrgId?: string }>();
    const navigation = useNavigation();

    const { 
        data: postDataResponse, 
        isLoading,
        error,
        refetch 
    } = useFetchPostDetails({ postId: params.postsId });

    const post = postDataResponse;
    
    const styles = getStyles(activeTheme);

    const [imageViewerVisible, setImageViewerVisible] = useState(false);
    const [imageViewerInitialIndex, setImageViewerInitialIndex] = useState(0);
    const [isVideoModalVisible, setVideoModalVisible] = useState(false);
    const [currentVideoUrl, setCurrentVideoUrl] = useState<string | null>(null);

    const openVideoModal = (url: string) => {
        setCurrentVideoUrl(url);
        setVideoModalVisible(true);
    };
    
    const openImageViewer = (index: number = 0) => {
        setImageViewerInitialIndex(index);
        setImageViewerVisible(true);
    };

    const closeImageViewer = () => {
        setImageViewerVisible(false);
    };

    const getValidImageUrl = (filePath: string | undefined): string | undefined => {
        if (!filePath) return undefined;
        try {
            const url = new URL(filePath, MEDIA_BASE_URL);
            return new URL(url.pathname, MEDIA_BASE_URL).toString();
        } catch (e) {
            return undefined;
        }
    };

    const handleShare = async () => {
        if (!post) return;
        try {
            const shareContent = generatePostShareContent({
                post: {
                    id: post.id,
                    title: post.title,
                    updatedAt: post.updated_at || '',
                    author: post.author_display_name || '',
                },
                t,
                language: i18n.language
            });
            await Share.share(shareContent);
        } catch (error) {
            console.error('Share error:', error);
        }
    };

    useLayoutEffect(() => {
        navigation.setOptions({
            title: t('posts.detail.title'),
        });
    }, [navigation, t, i18n.language]);

    if (isLoading) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={activeTheme.colors.primary} />
            </View>
        );
    }

    if (!post || error) {
        return (
            <ErrorView
                onGoBack={() => router.back()}
            />
        );
    }

    return (
        <>
            <Stack.Screen 
                options={{
                    headerShown: true,
                    headerTitle: t('posts.detail.title'),
                    headerRight: () => (
                        <View style={styles.headerRight}>
                            <TouchableOpacity onPress={handleShare} style={styles.headerButton}>
                                <MaterialCommunityIcons name="share-variant" size={24} color={activeTheme.system.text} />
                            </TouchableOpacity>
                        </View>
                    )
                }}
            />
            <View style={styles.container}>
                <ScrollView 
                    style={styles.scrollView}
                    contentContainerStyle={styles.scrollContentContainer}
                >
                    <ImageCarousel
                        mediaItems={post?.media_items || []}
                        onImagePress={openImageViewer}
                        theme={activeTheme}
                    />

                    <View style={styles.contentContainer}>
                        {post?.tags && post.tags.length > 0 && (
                            <View style={styles.categoryRow}>
                                <Text style={styles.category}>
                                    {post.tags[0] 
                                        ? `${(post.tags[0] as any).name_en?.toUpperCase() || 'POST'} / ${(post.tags[0] as any).name_zh_hk || '新聞'}`
                                        : 'POST / 新聞'
                                    }
                                </Text>
                            </View>
                        )}
                        <Text style={styles.title}>{post?.title}</Text>
                        <View style={styles.metaContainer}>
                            <View style={styles.authorContainer}>
                                <MaterialCommunityIcons name="account" size={16} color={activeTheme.system.secondaryText} />
                                <Text style={styles.authorName}>
                                    {post?.author_display_name || t('common.noAuthor')}
                                </Text>
                            </View>
                            <View style={styles.dateContainer}>
                                <MaterialCommunityIcons name="clock-outline" size={16} color={activeTheme.system.secondaryText} />
                                <Text style={styles.date}>
                                    {formatPostDate(post?.updated_at)}
                                </Text>
                            </View>
                        </View>

                        {post?.content && (
                            <RichTextDescription description={post.content} />
                        )}

                        {post?.media_items && post.media_items.length > 0 && (
                            (() => {
                                const nonImageItems = post.media_items.filter(item => !isImageFile(getFileType(item.file_name)));
                                return nonImageItems.length > 0 ? (
                                    <View style={styles.attachmentsSection}>
                                        <Text style={styles.sectionTitle}>{t('posts.detail.attachments')}</Text>
                                        <View style={styles.attachmentsContainer}>
                                            {nonImageItems.map((item, index) => (
                                                <AttachmentItem 
                                                    key={index}
                                                    attachment={{
                                                        type: getFileType(item.file_name),
                                                        url: getValidImageUrl(item.file_path) || '',
                                                        name: item.file_name,
                                                        file_path: item.file_path,
                                                        file_size: item.file_size,
                                                    }}
                                                    theme={activeTheme} 
                                                    styles={styles}
                                                    t={t}
                                                    openVideoModal={openVideoModal}
                                                    isLast={index === nonImageItems.length - 1}
                                                />
                                            ))}
                                        </View>
                                    </View>
                                ) : null;
                            })()
                        )}
                    </View>
                </ScrollView>
            </View>

            <ImageViewer
                visible={imageViewerVisible}
                images={post?.media_items || []}
                initialIndex={imageViewerInitialIndex}
                onClose={closeImageViewer}
                theme={activeTheme}
            />

            {currentVideoUrl && (
                <Modal
                    visible={isVideoModalVisible}
                    transparent={true}
                    animationType="fade"
                    onRequestClose={() => {
                        setVideoModalVisible(false);
                        setCurrentVideoUrl(null);
                    }}
                >
                    <View style={styles.videoModalContainer}>
                        <View style={styles.videoPlayerView}>
                            <Text style={styles.videoModalText}>
                                Video player (e.g., WebView) for {currentVideoUrl} should be here.
                            </Text>
                            <TouchableOpacity 
                                onPress={() => {
                                    setVideoModalVisible(false);
                                    setCurrentVideoUrl(null);
                                }}
                                style={styles.videoCloseButton}
                            >
                                <MaterialCommunityIcons name="close-circle" size={30} color="#fff" />
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>
            )}
        </>
    );
};

const getStyles = (theme: any) => StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    scrollView: {
        flex: 1,
        backgroundColor: theme.system.background,
    },
    scrollContentContainer: {
        paddingBottom: 40,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.colors.background,
    },
    headerRight: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    headerButton: {
        paddingHorizontal: 16,
    },
    contentContainer: {
        backgroundColor: theme.system.background,
        padding: 20,
        marginTop: -24,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    categoryRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    category: {
        fontSize: 12,
        fontWeight: '600',
        letterSpacing: 1,
        color: theme.colors.primary,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        color: theme.system.text,
        marginBottom: 8,
    },
    metaContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 16,
    },
    authorContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    dateContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    authorName: {
        fontSize: 14,
        color: theme.system.secondaryText,
        marginLeft: 4,
    },
    date: {
        fontSize: 14,
        color: theme.system.secondaryText,
        marginLeft: 4,
    },
    attachmentsSection: {
        marginTop: 24,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: theme.system.text,
        marginBottom: 12,
    },
    attachmentsContainer: {
        backgroundColor: theme.system.background,
        borderRadius: 12,
        overflow: 'hidden',
    },
    attachmentLastItem: {
        borderBottomWidth: 0,
    },
    attachmentListItem: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        backgroundColor: theme.system.background,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: theme.system.border,
        minHeight: 64,
    },
    attachmentIconContainer: {
        width: 40,
        alignItems: 'center',
        justifyContent: 'flex-start',
        marginRight: 12,
        paddingTop: 2,
    },
    attachmentTextContainer: {
        flex: 1,
        justifyContent: 'center',
        gap: 2,
    },
    attachmentTitle: {
        fontSize: 16,
        fontWeight: '500',
        color: theme.system.text,
        lineHeight: 20,
    },
    attachmentSubtitle: {
        fontSize: 14,
        color: theme.system.secondaryText,
        lineHeight: 18,
    },
    attachmentArrowContainer: {
        width: 40,
        alignItems: 'center',
        justifyContent: 'flex-start',
        marginLeft: 12,
        paddingTop: 2,
    },
    closeButton: {
        marginLeft: 10, 
        padding: 5,
    },
    videoModalContainer: {
        flex: 1, 
        justifyContent: 'center', 
        alignItems: 'center', 
        backgroundColor: 'rgba(0,0,0,0.7)'
    },
    videoPlayerView: {
        backgroundColor: '#000', 
        padding: 0,
        borderRadius: 0,
        width: '100%', 
        height: SCREEN_HEIGHT * 0.4, 
    },
    videoModalText: {
        color: '#fff', 
        padding: 20, 
        textAlign: 'center'
    },
    videoCloseButton: {
        position: 'absolute', 
        top: 10, 
        right: 10, 
        padding: 10, 
        zIndex: 1
    },
});

export default PostsDetailScreen;
