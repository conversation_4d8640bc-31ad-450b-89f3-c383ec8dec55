import { create } from 'zustand';
import { createTheme } from '@/theme/index';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface appStyleState {
    theme: any;
    currentThemeName: string;
    isUpdating: boolean;
    setTheme: (themeName: string) => void;
    setThemeWithoutPersist: (themeName: string) => void;
}

const initialThemeName = 'red';

// Cache themes to prevent recreation and infinite loops
const themeCache = new Map<string, any>();

const getCachedTheme = (themeName: string) => {
    if (!themeCache.has(themeName)) {
        themeCache.set(themeName, createTheme(themeName));
    }
    return themeCache.get(themeName);
};

export const appStyleStore = create<appStyleState>()(
    persist(
        (set, get) => ({
            theme: getCachedTheme(initialThemeName),
            currentThemeName: initialThemeName,
            isUpdating: false,
            setTheme: (themeName: string) => {
                const currentState = get();
                // Prevent infinite loops by checking if already updating or same theme
                if (currentState.isUpdating || themeName === currentState.currentThemeName) {
                    return;
                }

                set({ isUpdating: true });

                try {
                    const newTheme = getCachedTheme(themeName);
                    set({
                        theme: newTheme,
                        currentThemeName: themeName,
                        isUpdating: false
                    });
                } catch (error) {
                    console.error('[appStyleStore] Error setting theme:', error);
                    set({ isUpdating: false });
                }
            },
            setThemeWithoutPersist: (themeName: string) => {
                const currentState = get();
                if (currentState.isUpdating || themeName === currentState.currentThemeName) {
                    return;
                }

                const newTheme = getCachedTheme(themeName);
                set({
                    theme: newTheme,
                    currentThemeName: themeName
                });
            },
        }),
        {
            name: 'app-style-storage',
            storage: createJSONStorage(() => AsyncStorage),
            // Only persist theme name, not the entire theme object
            partialize: (state) => ({ currentThemeName: state.currentThemeName }),
            onRehydrateStorage: () => (state) => {
                if (state) {
                    // Recreate theme from persisted theme name
                    state.theme = getCachedTheme(state.currentThemeName);
                    state.isUpdating = false;
                }
            },
        }
    )
);