import React, { useState, useEffect } from 'react';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import BaseCodeVerificationScreen from './components/BaseCodeVerificationScreen';
import type { PKCECredentials } from '@/utils/pkce';

import {
    useExistingPhoneOtpVerify,
    useNewPhoneOtpVerify,
    useExistingPhoneOtpInitiate,
    useNewPhoneOtpInitiate,
    generatePKCECredentials
} from '@/api/authentication_services';
import type {
    ExistingPhoneOtpVerifyRequest,
    NewPhoneOtpVerifyRequest,
    ExistingPhoneOtpInitiateRequest,
    NewPhoneOtpInitiateRequest,
} from '@/api/api_config';

export default function CodeVerificationScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams<{ phoneNumber: string; isNewUser: string; method?: string; state?: string; flow_id?: string; codeVerifier?: string }>();
  
  // API Hooks
  const { mutateAsync: loginWithOtpMutation } = useExistingPhoneOtpVerify();
  const { mutateAsync: verifyRegistrationOtpMutation } = useNewPhoneOtpVerify();
  const { mutateAsync: initiateOtpLoginMutation } = useExistingPhoneOtpInitiate();
  const { mutateAsync: initiateRegistrationOtpMutation } = useNewPhoneOtpInitiate();

  const phoneNumber = params.phoneNumber || "";
  const isNewUser = params.isNewUser === '1';
  const verificationMethod = (params.method || 'sms') as 'sms' | 'whatsapp';
  const stateFromInitiate = params.state; // This is crucial for PKCE
  const codeVerifierFromInitiate = params.codeVerifier;

  const [displayName, setDisplayName] = useState('');
  const [currentPkce, setCurrentPkce] = useState<PKCECredentials | null>(null);

  useEffect(() => {
    if (stateFromInitiate && codeVerifierFromInitiate) {
      setCurrentPkce({ state: stateFromInitiate, codeVerifier: codeVerifierFromInitiate, codeChallenge: '' });
    }
  }, [stateFromInitiate, codeVerifierFromInitiate]);

  // useEffect(() => {
  //   // console.log('Auth CodeVerificationScreen loaded');
  //   // console.log('Phone number:', phoneNumber);
  //   // console.log('User type:', isNewUser ? 'New User (Registration)' : 'Existing User (Login)');
  //   // console.log('Verification method:', verificationMethod);
  //   // console.log('State from initiate:', stateFromInitiate);
  //   if (!stateFromInitiate) {
  //     // console.warn('[CodeVerificationScreen] State parameter is missing from navigation. PKCE flow might fail.');
  //   }
  // }, [phoneNumber, isNewUser, verificationMethod, stateFromInitiate]);

  const getErrorKeyFromStatus = (status?: number, error?: string) => {
    // Handle specific server error messages first
    if (error) {
      // Exact matches for backend error messages
      if (error === 'Invalid OTP') return 'INVALID_CODE';
      if (error.includes('Too many failed OTP attempts')) return 'ACCOUNT_LOCKED';
      if (error.includes('Account locked')) return 'ACCOUNT_LOCKED';
      if (error === 'OTP has expired') return 'CODE_EXPIRED';
      if (error === 'State is required') return 'INVALID_REQUEST';
      if (error === 'OTP is required') return 'INVALID_REQUEST';
      if (error === 'Code verifier is required') return 'INVALID_REQUEST';
      // Generic fallback for unrecognized error messages
      return 'SYSTEM_ERROR';
    }
    
    // Fallback to status code mapping when no specific error message
    switch (status) {
      case 400: return 'INVALID_CODE';
      case 401: return 'UNAUTHORIZED';
      case 403: return 'ACCOUNT_LOCKED'; // 403 for OTP verification means account locked
      case 404: return 'INVALID_REQUEST';
      case 429: return 'RATE_LIMIT_EXCEEDED'; // 429 is for rate limiting
      case 500: case 502: case 503: case 504: return 'SYSTEM_ERROR';
      default: return 'SYSTEM_ERROR';
    }
  };

  const handleVerifyCode = async (code: string) => {
    if (!currentPkce || !currentPkce.state || !currentPkce.codeVerifier) {
      // console.error('PKCE state or verifier is missing. Cannot verify code.');
      return {
        success: false,
        error: t('error.phoneVerification.INVALID_REQUEST'),
        errorCode: 'INVALID_REQUEST'
      };
    }

    try {
      if (isNewUser) {
        if (!displayName.trim()) {
          return {
            success: false,
            error: t('error.phoneVerification.DISPLAY_NAME_REQUIRED'),
            errorCode: 'DISPLAY_NAME_REQUIRED'
          };
        }
        const verifyPayload: NewPhoneOtpVerifyRequest = {
          otp: code,
          state: currentPkce.state,
          code_verifier: currentPkce.codeVerifier,
          display_name: displayName,
          phone_otp_channel: verificationMethod as 'whatsapp'
        };
        await verifyRegistrationOtpMutation(verifyPayload);
      } else {
        const verifyPayload: ExistingPhoneOtpVerifyRequest = {
          otp: code,
          state: currentPkce.state,
          code_verifier: currentPkce.codeVerifier,
        };
        await loginWithOtpMutation(verifyPayload);
      }

      return { success: true };
    } catch (error: any) {
      const serverErrorCode = error.response?.data?.error;
      const statusCode = error.response?.status;
      const errorKey = getErrorKeyFromStatus(statusCode, serverErrorCode);
      const translatedError = t(`error.phoneVerification.${errorKey}`, {
        defaultValue: t('error.phoneVerification.SYSTEM_ERROR')
      });
      return {
        success: false,
        error: translatedError,
        errorCode: errorKey,
        statusCode
      };
    }
  };

  const handleResendCode = async () => {
    // Use the same client_id logic as web app: 852 + local phone number
    const clientId = phoneNumber.replace('+', '');
    
    try {
      const newPkceCredentials = await generatePKCECredentials(); // Use the imported function

      let resendResponse;
      if (!isNewUser) {
        const resendPayload: ExistingPhoneOtpInitiateRequest = {
            phone: phoneNumber,
            phone_otp_channel: verificationMethod as 'whatsapp', // Assuming whatsapp if not sms
            client_id: clientId,
            state: newPkceCredentials.state, // Send client-generated state
            code_challenge: newPkceCredentials.codeChallenge,
            code_challenge_method: 'S256'
        };
        resendResponse = await initiateOtpLoginMutation(resendPayload);
      } else {
        const resendPayload: NewPhoneOtpInitiateRequest = {
            phone: phoneNumber,
            phone_otp_channel: verificationMethod as 'whatsapp', // Assuming whatsapp if not sms
            client_id: clientId,
            state: newPkceCredentials.state, // Send client-generated state
            code_challenge: newPkceCredentials.codeChallenge,
            code_challenge_method: 'S256'
        };
        resendResponse = await initiateRegistrationOtpMutation(resendPayload);
      }
      
      if (resendResponse && resendResponse.state) {
        // IMPORTANT: Use the state from the API response for the next verification step.
        // The code verifier is the one we just generated.
        setCurrentPkce({
          codeVerifier: newPkceCredentials.codeVerifier,
          state: resendResponse.state, // Authoritative state from server response
          codeChallenge: newPkceCredentials.codeChallenge // Store for completeness if needed elsewhere
        });
        // console.log('Resend OTP successful. New PKCE context set:', { state: resendResponse.state, codeVerifier: newPkceCredentials.codeVerifier });
        return { success: true }; // Resend successful
      } else {
        // Resend failed or API response was malformed (e.g., missing state)
        // console.error('Resend OTP failed: API response missing state or invalid response.', resendResponse);
        const translatedError = t('error.phoneVerification.RESEND_FAILED_NO_STATE', {
            defaultValue: t('error.phoneVerification.SYSTEM_ERROR_RESEND')
        });
        return {
          success: false,
          error: translatedError,
          errorCode: 'RESEND_FAILED_NO_STATE'
        };
      }
    } catch (error: any) {
      // This catch block handles API errors from initiateOtpLoginMutation or initiateRegistrationOtpMutation
      // console.error('Resend OTP API error:', error);
      const serverErrorCode = error.response?.data?.error;
      const statusCode = error.response?.status;
      const errorKey = getErrorKeyFromStatus(statusCode, serverErrorCode);
      const translatedError = t(`error.phoneVerification.${errorKey}`, {
        defaultValue: t('error.phoneVerification.SYSTEM_ERROR')
      });
      return {
        success: false,
        error: translatedError,
        errorCode: errorKey,
        statusCode
      };
    }
  };

  const handleSuccessNavigation = () => {
    router.dismissAll();
    if (isNewUser) {
      router.replace({
        pathname: '/tabs/dashboard',
        params: { newRegistration: 'true', displayName: displayName },
      });
    } else {
      router.replace('/tabs/dashboard'); 
    }
  };

  return (
    <BaseCodeVerificationScreen
      phoneNumber={phoneNumber}
      verificationMethod={verificationMethod}
      onVerify={handleVerifyCode}
      onResendCode={handleResendCode}
      onSuccessNavigation={handleSuccessNavigation}
      screenStackTitleKey={isNewUser ? "auth.signUp" : "auth.login"}
      headerSubtitleKey="auth.codeVerificationDesc"
      showDisplayNameInput={isNewUser}
      initialDisplayName={displayName}
      onDisplayNameChange={setDisplayName}
      isNewUserFlow={isNewUser}
      displayNameLabelKey="auth.enterDisplayName"
      displayNamePlaceholderKey="auth.displayNamePlaceholder"
    />
  );
}