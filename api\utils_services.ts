import {
  useQuery,
  // useMutation, // No mutations for utils in ApiConfig
  useQueryClient,
} from '@tanstack/react-query';
import axiosInstance from './axios_instance';
import { AxiosError } from 'axios';
import { authenticationStore } from '@/stores/authentication_store';
import {
  fetchIntervalMs,
  ApiConfig,
  type VerificationTypesPullRequest,
  type GovernmentFundingTypesPullRequest,
  type TypesPullResponse, // Shared response type for both
} from '@/api/api_config';
import {
  verificationTypesStore,
  governmentFundingTypesStore,
} from 'stores/utils_store';
import { useEffect, useMemo } from 'react';

// Fetch Verification Types
export const useFetchVerificationTypes = (requestParams: VerificationTypesPullRequest) => {
  const queryClient = useQueryClient(); // Added queryClient
  const queryKeyConst = ['verificationTypes', requestParams] as const; // Corrected queryKey
  const queryResult = useQuery<TypesPullResponse, Error, TypesPullResponse, typeof queryKeyConst>(
    {
      queryKey: queryKeyConst, 
      queryFn: async () => {
        try {
          const response = await axiosInstance.request<TypesPullResponse>({
            url: ApiConfig.utils.verification_types_pull.endpoint,
            method: ApiConfig.utils.verification_types_pull.method,
            // params: requestParams, // If GET and params are in query string
            params: requestParams, // If GET with body or other methods
            headers: { 'Content-Type': 'application/json' },
          });
          return response.data;
        } catch (error) {
          throw error;
        }
      },
      // enabled: !!requestParams.lang_code, // Enable only if lang_code is present, if it's truly optional at call site
    }
  );

  // Memoize the stable values to prevent unnecessary effect runs
  const stableRequestParams = useMemo(() => requestParams, [requestParams.lang_code]);

  useEffect(() => {
    const store = verificationTypesStore.getState();
    if (queryResult.isLoading) {
      // console.log('[utils_services] useFetchVerificationTypes is fetching');
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setVerificationTypes(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
      console.log(`[utils_services] verificationTypesStore setVerificationTypes successful`);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, stableRequestParams]);

  return queryResult;
};

// Fetch Government Funding Types
export const useFetchGovernmentFundingTypes = (requestParams: GovernmentFundingTypesPullRequest) => {
  const queryClient = useQueryClient(); // Added queryClient
  const queryKeyConst = ['governmentFundingTypes', requestParams] as const; // Corrected queryKey
  const queryResult = useQuery<TypesPullResponse, Error, TypesPullResponse, typeof queryKeyConst>(
    {
      queryKey: queryKeyConst, 
      queryFn: async () => {
        try {
          const response = await axiosInstance.request<TypesPullResponse>({
            url: ApiConfig.utils.government_funding_types_pull.endpoint,
            method: ApiConfig.utils.government_funding_types_pull.method,
            // params: requestParams, // If GET and params are in query string
            params: requestParams, // If GET with body or other methods
            headers: { 'Content-Type': 'application/json' },
          });
          return response.data;
        } catch (error) {
          throw error;
        }
      },
      // enabled: !!requestParams.lang_code, // Enable only if lang_code is present
    }
  );

  // Memoize the stable values to prevent unnecessary effect runs
  const stableRequestParams = useMemo(() => requestParams, [requestParams.lang_code]);
  
  useEffect(() => {
    const store = governmentFundingTypesStore.getState();
    if (queryResult.isLoading) {
      // console.log('[utils_services] useFetchGovernmentFundingTypes is fetching');
    } else if (queryResult.isError && queryResult.error) {
      store.setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setGovernmentFundingTypes(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
      console.log(`[utils_services] governmentFundingTypesStore setGovernmentFundingTypes successful`);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, stableRequestParams]);

  return queryResult;
};
