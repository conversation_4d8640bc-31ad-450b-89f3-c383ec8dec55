import { MD3Theme } from 'react-native-paper';

declare module 'react-native-paper' {
    export interface MD3Theme {
        colors: {
            primary: string;
            primaryContainer: string;
            secondary: string;
            secondaryContainer: string;
            primaryLight: string;
            tertiary: string;
            tertiaryContainer: string;
            surface: string;
            surfaceVariant: string;
            surfaceDisabled: string;
            background: string;
            error: string;
            errorContainer: string;
            onPrimary: string;
            onPrimaryContainer: string;
            onSecondary: string;
            onSecondaryContainer: string;
            onTertiary: string;
            onTertiaryContainer: string;
            onSurface: string;
            onSurfaceVariant: string;
            onSurfaceDisabled: string;
            onError: string;
            onErrorContainer: string;
            onBackground: string;
            outline: string;
            outlineVariant: string;
            inverseSurface: string;
            inverseOnSurface: string;
            inversePrimary: string;
            shadow: string;
            scrim: string;
            backdrop: string;
            elevation: {
                level0: string;
                level1: string;
                level2: string;
                level3: string;
                level4: string;
                level5: string;
            };
        };
        system: {
            participant: string;
            volunteer: string;
            text: string;
            secondaryText: string;
            background: string;
            border: string;
            success: string;
            error: string;
            info: string;
            warning: string;
        };
    }
} 