import { create } from 'zustand';
import { AxiosError } from 'axios';
import type {
    OrganizationListPayload,
} from '@/api/api_config';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { appStyleStore } from './app_style_store';

interface OrganizationListState {
    organizationList: OrganizationListPayload[];
    isFetching: boolean;
    error: AxiosError | null;
    setOrganizationList: (organizations: OrganizationListPayload[]) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const organizationListStore = create<OrganizationListState>()(
    persist(
        (set) => ({
            organizationList: [],
            isFetching: false,
            error: null,
            setOrganizationList: (organizations: OrganizationListPayload[]) => set({ organizationList: organizations, isFetching: false, error: null }),
            setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
            setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
        }),
        {
            name: 'organization-list-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

interface OrganizationDetailsState {
    organizationDetails: OrganizationListPayload | null;
    isFetching: boolean;
    error: AxiosError | null;
    setOrganizationDetails: (organization: OrganizationListPayload | null) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const organizationDetailsStore = create<OrganizationDetailsState>()(
    persist(
        (set) => ({
            organizationDetails: null,
            isFetching: false,
            error: null,
            setOrganizationDetails: (organization: OrganizationListPayload | null) => set({ organizationDetails: organization, isFetching: false, error: null }),
            setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
            setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
        }),
        {
            name: 'organization-details-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

interface OrganizationState {
    selectedOrganization: OrganizationListPayload | null;
    setSelectedOrganization: (organization: OrganizationListPayload | null) => void;
}

export const organizationStore = create<OrganizationState>()(persist(
    (set) => ({
        selectedOrganization: null,
        setSelectedOrganization: (organization: OrganizationListPayload | null) => {
            set({ selectedOrganization: organization });
            appStyleStore.getState().setTheme(organization?.theme_color ?? 'red');
        },
    }),
    {
        name: 'organization-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));