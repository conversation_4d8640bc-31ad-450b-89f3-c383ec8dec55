import { create } from 'zustand';
import { AxiosError } from 'axios';
import type {
    OrganizationListPayload,
} from '@/api/api_config';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { appStyleStore } from './app_style_store';

interface OrganizationListState {
    organizationList: OrganizationListPayload[];
    isFetching: boolean;
    error: AxiosError | null;
    setOrganizationList: (organizations: OrganizationListPayload[]) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const organizationListStore = create<OrganizationListState>()(
    persist(
        (set) => ({
            organizationList: [],
            isFetching: false,
            error: null,
            setOrganizationList: (organizations: OrganizationListPayload[]) => set({ organizationList: organizations, isFetching: false, error: null }),
            setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
            setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
        }),
        {
            name: 'organization-list-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

interface OrganizationDetailsState {
    organizationDetails: OrganizationListPayload | null;
    isFetching: boolean;
    error: AxiosError | null;
    setOrganizationDetails: (organization: OrganizationListPayload | null) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const organizationDetailsStore = create<OrganizationDetailsState>()(
    persist(
        (set) => ({
            organizationDetails: null,
            isFetching: false,
            error: null,
            setOrganizationDetails: (organization: OrganizationListPayload | null) => set({ organizationDetails: organization, isFetching: false, error: null }),
            setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
            setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
        }),
        {
            name: 'organization-details-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

interface OrganizationState {
    selectedOrganization: OrganizationListPayload | null;
    isUpdating: boolean;
    setSelectedOrganization: (organization: OrganizationListPayload | null) => void;
    setSelectedOrganizationWithTheme: (organization: OrganizationListPayload | null) => void;
}

export const organizationStore = create<OrganizationState>()(persist(
    (set, get) => ({
        selectedOrganization: null,
        isUpdating: false,
        setSelectedOrganization: (organization: OrganizationListPayload | null) => {
            const currentState = get();
            // Prevent infinite loops by checking if already updating or same organization
            if (currentState.isUpdating ||
                (currentState.selectedOrganization?.id === organization?.id)) {
                return;
            }

            set({ selectedOrganization: organization });
        },
        setSelectedOrganizationWithTheme: (organization: OrganizationListPayload | null) => {
            const currentState = get();
            // Prevent infinite loops
            if (currentState.isUpdating ||
                (currentState.selectedOrganization?.id === organization?.id)) {
                return;
            }

            set({ isUpdating: true });

            try {
                set({ selectedOrganization: organization });

                // Update theme only if organization has changed and has a theme
                const themeColor = organization?.theme_color ?? 'red';
                const appStyleState = appStyleStore.getState();

                if (themeColor !== appStyleState.currentThemeName) {
                    appStyleState.setTheme(themeColor);
                }
            } catch (error) {
                console.error('[organizationStore] Error setting organization with theme:', error);
            } finally {
                set({ isUpdating: false });
            }
        },
    }),
    {
        name: 'organization-storage',
        storage: createJSONStorage(() => AsyncStorage),
        // Only persist the organization data, not the updating state
        partialize: (state) => ({ selectedOrganization: state.selectedOrganization }),
        onRehydrateStorage: () => (state) => {
            if (state) {
                state.isUpdating = false;
            }
        },
    }
));