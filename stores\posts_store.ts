import { create } from 'zustand';
import { AxiosError } from 'axios';
import type {
    PostListPayload,
    PostTagsPullResponse,
    PublishedPostsListPullRequest,
} from '@/api/api_config';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

// State for Published Posts List
interface PostsDashboardListState {
    postsList: PostListPayload[];
    isFetching: boolean; // Standardized name
    error: AxiosError | null; // Standardized name
    setPostsList: (list: PostListPayload[]) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const postsDashboardListStore = create<PostsDashboardListState>()(
    persist(
        (set) => ({
            postsList: [],
            isFetching: false,
            error: null,
            setPostsList: (list) => set({ postsList: list, isFetching: false, error: null }),
            setError: (error) => set({ error: error, isFetching: false }),
            setIsFetching: (isFetching) => set({ isFetching: isFetching }),
        }),
        {
            name: 'posts-dashboard-list-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

interface PostsExploreListState {
    postsList: PostListPayload[];
    isFetching: boolean; // Standardized name
    error: AxiosError | null; // Standardized name
    setPostsList: (list: PostListPayload[]) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const postsExploreListStore = create<PostsExploreListState>()(
    persist(
        (set) => ({
            postsList: [],
            isFetching: false,
            error: null,
            setPostsList: (list) => set({ postsList: list, isFetching: false, error: null }),
            setError: (error) => set({ error: error, isFetching: false }),
            setIsFetching: (isFetching) => set({ isFetching: isFetching }),
        }),
        {
            name: 'posts-explore-list-storage',
            storage: createJSONStorage(() => AsyncStorage),
        }
    )
);

// State for Post Details
interface PostDetailsState {
    postDetails: PostListPayload | null;
    isFetching: boolean; // Standardized name
    error: AxiosError | null; // Standardized name
    setPostDetails: (details: PostListPayload) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const postDetailsStore = create<PostDetailsState>()(persist(
    (set) => ({
        postDetails: null,
        isFetching: false,
        error: null,
        setPostDetails: (details) => set({ postDetails: details, isFetching: false, error: null }),
        setError: (error) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching) => set({ isFetching: isFetching }),
    }),
    {
        name: 'post-details-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));

// State for Post Tags
interface PostTagsState {
    postTags: PostTagsPullResponse;
    isFetching: boolean; // Standardized name
    error: AxiosError | null; // Standardized name
    setPostTags: (tags: PostTagsPullResponse) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const postTagsStore = create<PostTagsState>()(persist(
    (set) => ({
        postTags: { tags: [] },
        isFetching: false,
        error: null,
        setPostTags: (tags) => set({ postTags: tags, isFetching: false, error: null }),
        setError: (error) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching) => set({ isFetching: isFetching }),
    }),
    {
        name: 'post-tags-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));

interface PostsExploreFilterState {
    filter: PublishedPostsListPullRequest;
    setFilter: (filter: PublishedPostsListPullRequest) => void;
}

export const postsExploreFilterListStore = create<PostsExploreFilterState>()(persist(
    (set) => ({
        filter: {},
        setFilter: (filter) => set({ filter }),
    }),
    {
        name: 'posts-explore-filter-list-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));
