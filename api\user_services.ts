import {
  useQuery,
  useMutation,
  useQueryClient,
  type UseQueryOptions, // Ensure UseQueryOptions is imported
  type QueryKey, // Import QueryKey
  // type UseMutationOptions, // Not strictly needed if options are inline
} from '@tanstack/react-query';
import axiosInstance from './axios_instance';
import { AxiosError } from 'axios';
import axios from 'axios';
import { authenticationStore } from '@/stores/authentication_store';
import { API_BASE_URL, ApiConfig, type AccessTokenRefreshResponse } from '@/api/api_config';
import {
  fetchIntervalMs,
  type ProfilePullRequest,
  type ProfilePushRequest,
  type ProfileUploadIconRequest,
  type ChangePhoneOtpInitiateRequest,
  type ChangePhoneOtpVerifyRequest,
  type OrganizationListPullRequest,
  type StatisticsPullRequest,
  type UserIdPullRequest,
  type VerificationsPullRequest,
  type VerificationsPushRequest,
  type VerificationsDetailsPullRequest,
  type VolunteerOrganizationQualificationsPullRequest,
  type ProfileResponse,
  type PhoneOtpInitiateResponse,
  type ChangePhoneOtpVerifyResponse,
  type OrganizationListPayload,
  type StatisticsPullResponse,
  type UserIdPullResponse,
  type VerificationPayload,
  type VolunteerQualificationPayload,
} from '@/api/api_config';
import {
  userProfileStore,
  userOrganizationsStore,
  userStatisticsStore,
  userIdStore,
  userVerificationsStore,
  userVerificationDetailsStore,
  userVolunteerQualificationsStore,
} from 'stores/user_store';
import { useEffect } from 'react';
import * as Crypto from 'expo-crypto';
import { encode as btoa } from 'base-64';
import { handleAuthenticationError } from '@/utils/authUtils';



// Fetch User Profile
export const useFetchUserProfile = (requestParams?: ProfilePullRequest) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['userProfile', requestParams] as const;
  
  // Check if user is authenticated before making request
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  const queryResult = useQuery<ProfileResponse, Error, ProfileResponse, typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<ProfileResponse>({
          url: ApiConfig.user.profile_pull.endpoint,
          method: ApiConfig.user.profile_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated, // Only fetch when user is authenticated
    // onError: (error: Error) => { // Deprecated
    //     userProfileStore.getState().setError(error as any);
    //     handleApiError(error, queryClient, 'user_services (useFetchUserProfile)', requestParams, queryKeyConst);
    // }
  });

  useEffect(() => {
    const store = userProfileStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      userProfileStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setProfile(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams, queryClient]);

  return queryResult;
};

// Update User Profile
export const useUpdateUserProfile = () => {
  const queryClient = useQueryClient();
  return useMutation<ProfileResponse, Error, ProfilePushRequest>({
    mutationFn: async (payload: ProfilePushRequest): Promise<ProfileResponse> => {
      try {
        const response = await axiosInstance.request<ProfileResponse>({
          url: ApiConfig.user.profile_push.endpoint,
          method: ApiConfig.user.profile_push.method,
          data: payload,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: ProfileResponse) => {
      console.log(`[user_services] useUpdateUserProfile successful, response: ${JSON.stringify(data)}`);
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      userProfileStore.getState().setProfile(data);
    },
    onError: (error: Error, variables: ProfilePushRequest) => {
      console.error(`[user_services] useUpdateUserProfile error: ${error}, request: ${JSON.stringify(variables)}`);
      // Token handling is now managed by axios interceptor
    }
  });
};

// Upload Profile Icon
export const useUploadProfileIcon = () => {
  const queryClient = useQueryClient();
  return useMutation<ProfileResponse, Error, FormData >({
    mutationFn: async (formData: FormData): Promise<ProfileResponse> => {
      try {
        const response = await axiosInstance.request<ProfileResponse>({
          url: ApiConfig.user.profile_upload_icon.endpoint,
          method: ApiConfig.user.profile_upload_icon.method,
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: ProfileResponse) => {
      console.log(`[user_services] useUploadProfileIcon successful, response: ${JSON.stringify(data)}`);
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      userProfileStore.getState().setProfile(data);
    },
    onError: (error: Error, variables: FormData) => {
      console.error(`[user_services] useUploadProfileIcon error: ${error}`);
      // Token handling is now managed by axios interceptor
    }
  });
};

// Change Phone OTP Initiate
export const useChangePhoneOtpInitiate = () => {
  const queryClient = useQueryClient();
  return useMutation<PhoneOtpInitiateResponse, Error, ChangePhoneOtpInitiateRequest>({
    mutationFn: async (payload: ChangePhoneOtpInitiateRequest): Promise<PhoneOtpInitiateResponse> => {
      try {
        const response = await axiosInstance.request<PhoneOtpInitiateResponse>({
          url: ApiConfig.user.change_phone_otp_initiate.endpoint,
          method: ApiConfig.user.change_phone_otp_initiate.method,
          data: payload,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: PhoneOtpInitiateResponse) => {
      console.log(`[user_services] useChangePhoneOtpInitiate successful, response: ${JSON.stringify(data)}`);
    },
    onError: (error: Error, variables: ChangePhoneOtpInitiateRequest) => {
      console.error(`[user_services] useChangePhoneOtpInitiate error: ${error}, request: ${JSON.stringify(variables)}`);
      // Token handling is now managed by axios interceptor
    }
  });
};

// Change Phone OTP Verify
export const useChangePhoneOtpVerify = () => {
  const queryClient = useQueryClient();
  return useMutation<ChangePhoneOtpVerifyResponse, Error, ChangePhoneOtpVerifyRequest>({
    mutationFn: async (payload: ChangePhoneOtpVerifyRequest): Promise<ChangePhoneOtpVerifyResponse> => {
      try {
        const response = await axiosInstance.request<ChangePhoneOtpVerifyResponse>({
          url: ApiConfig.user.change_phone_otp_verify.endpoint,
          method: ApiConfig.user.change_phone_otp_verify.method,
          data: payload,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: ChangePhoneOtpVerifyResponse) => {
      console.log(`[user_services] useChangePhoneOtpVerify successful, response: ${JSON.stringify(data)}`);
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      if (data.user) {
        userProfileStore.getState().setProfile(data.user);
      }
    },
    onError: (error: Error, variables: ChangePhoneOtpVerifyRequest) => {
      console.error(`[user_services] useChangePhoneOtpVerify error: ${error}, request: ${JSON.stringify(variables)}`);
      // Token handling is now managed by axios interceptor
    }
  });
};

// Define a specific type for the query key used in useFetchUserOrganizations
type UserOrganizationsQueryKey = readonly ['userOrganizations', OrganizationListPullRequest | undefined];

// Fetch User's Organization List
export const useFetchUserOrganizations = (
    requestParams?: OrganizationListPullRequest,
    queryOptions?: Omit<UseQueryOptions<OrganizationListPayload[], Error, OrganizationListPayload[], UserOrganizationsQueryKey>, 'queryKey' | 'queryFn'>
) => {
  const queryClient = useQueryClient();
  const queryKeyConst: UserOrganizationsQueryKey = ['userOrganizations', requestParams] as const;
  
  // Check if user is authenticated before making request
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  const queryResult = useQuery<OrganizationListPayload[], Error, OrganizationListPayload[], UserOrganizationsQueryKey>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<OrganizationListPayload[]>({
          url: ApiConfig.user.organization_list_pull.endpoint,
          method: ApiConfig.user.organization_list_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated, // Only fetch when user is authenticated
    // onError: (error: Error) => { // Deprecated
    //     userOrganizationsStore.getState().setError(error as any);
    //     handleApiError(error, queryClient, 'user_services (useFetchUserOrganizations)', requestParams, queryKeyConst);
    // }
    ...queryOptions // Spread the additional query options here
  });

  useEffect(() => {
    const store = userOrganizationsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else {
      store.setIsFetching(false); // Ensure isFetching is false when not loading
      if (queryResult.isError && queryResult.error) {
        store.setError(queryResult.error as any);
        // Token handling is now managed by axios interceptor
      } else if (queryResult.isSuccess && queryResult.data) {
        store.setUserOrganizations(queryResult.data);
        store.setError(null); // Clear error on success
      }
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams, queryClient]);

  return queryResult;
};

// Fetch User Statistics
export const useFetchUserStatistics = (requestParams?: StatisticsPullRequest) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['userStatistics', requestParams] as const;
  
  // Check if user is authenticated before making request
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);
  
  const queryResult = useQuery<StatisticsPullResponse, Error, StatisticsPullResponse, typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<StatisticsPullResponse>({
          url: ApiConfig.user.statistics_pull.endpoint,
          method: ApiConfig.user.statistics_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated, // Only fetch when user is authenticated
    // onError: (error: Error) => { // Deprecated
    //     userStatisticsStore.getState().setError(error as any);
    //     handleApiError(error, queryClient, 'user_services (useFetchUserStatistics)', requestParams, queryKeyConst);
    // }
  });

  useEffect(() => {
    const store = userStatisticsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      userStatisticsStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setStatistics(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams, queryClient]);

  return queryResult;
};

// Fetch User ID (UUID)
export const useFetchUserId = (requestParams?: UserIdPullRequest) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['userId', requestParams] as const;

  // Check if user is authenticated before making request
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  const queryResult = useQuery<UserIdPullResponse, Error, UserIdPullResponse, typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<UserIdPullResponse>({
          url: ApiConfig.user.user_id_pull.endpoint,
          method: ApiConfig.user.user_id_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated, // Only fetch when user is authenticated
    // onError: (error: Error) => { // Deprecated
    //     userIdStore.getState().setError(error as any);
    //     handleApiError(error, queryClient, 'user_services (useFetchUserId)', requestParams, queryKeyConst);
    // }
  });

  useEffect(() => {
    const store = userIdStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      userIdStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setUserId(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams, queryClient]);

  return queryResult;
};

// Fetch User Verifications List
export const useFetchUserVerifications = (requestParams?: VerificationsPullRequest) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['userVerifications', requestParams] as const;

  // Check if user is authenticated before making request
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  const queryResult = useQuery<VerificationPayload[], Error, VerificationPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<VerificationPayload[]>({
          url: ApiConfig.user.verifications_pull.endpoint,
          method: ApiConfig.user.verifications_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated, // Only fetch when user is authenticated
    // onError: (error: Error) => { // Deprecated
    //     userVerificationsStore.getState().setError(error as any);
    //     handleApiError(error, queryClient, 'user_services (useFetchUserVerifications)', requestParams, queryKeyConst);
    // }
  });

  useEffect(() => {
    const store = userVerificationsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      userVerificationsStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setVerifications(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams, queryClient]);

  return queryResult;
};

// Fetch User Verifications for Volunteer Assistance (by user ID)
export const useFetchUserVerificationsByUserId = (userId: string, enabled: boolean = true) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['userVerificationsByUserId', userId] as const;

  // Check if user is authenticated before making request
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  const queryResult = useQuery<VerificationPayload[], Error, VerificationPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        // Use volunteer endpoint to fetch user verifications by user ID
        // This requires volunteer/admin permissions
        const response = await axiosInstance.request<VerificationPayload[]>({
          url: ApiConfig.volunteer.volunteers_get_verifications.endpoint,
          method: ApiConfig.volunteer.volunteers_get_verifications.method,
          headers: { 'Content-Type': 'application/json' },
          params: {
            user_id: userId,
          },
        });
        return response.data;
      } catch (error) {
        console.error('[useFetchUserVerificationsByUserId] Error fetching user verifications:', error);
        throw error;
      }
    },
    enabled: isAuthenticated && enabled && !!userId, // Only fetch when user is authenticated, enabled, and userId is provided
  });

  return queryResult;
};

// Submit New Verification
export const useSubmitVerification = () => {
  const queryClient = useQueryClient();
  return useMutation<VerificationPayload, Error, FormData> ({
    mutationFn: async (formData: FormData): Promise<VerificationPayload> => {
      try {
        const response = await axiosInstance.request<VerificationPayload>({
          url: ApiConfig.user.verifications_push.endpoint,
          method: ApiConfig.user.verifications_push.method,
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: VerificationPayload) => {
      console.log(`[user_services] useSubmitVerification successful, response: ${JSON.stringify(data)}`);
      queryClient.invalidateQueries({ queryKey: ['userVerifications'] });
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
    },
    onError: (error: Error, variables: FormData) => {
      console.error(`[user_services] useSubmitVerification error: ${error}`);
      // Token handling is now managed by axios interceptor
    }
  });
};

// Submit Verification for Another User (Volunteer Assistance)
export const useSubmitVerificationForUser = () => {
  const queryClient = useQueryClient();
  return useMutation<VerificationPayload, Error, FormData> ({
    mutationFn: async (formData: FormData): Promise<VerificationPayload> => {
      try {
        const response = await axiosInstance.request<VerificationPayload>({
          url: ApiConfig.volunteer.volunteers_submit_verification.endpoint,
          method: ApiConfig.volunteer.volunteers_submit_verification.method,
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data: VerificationPayload) => {
      console.log(`[user_services] useSubmitVerificationForUser successful, response: ${JSON.stringify(data)}`);
      // Invalidate both general verifications and user-specific verifications
      queryClient.invalidateQueries({ queryKey: ['userVerifications'] });
      queryClient.invalidateQueries({ queryKey: ['userVerificationsByUserId'] });
    },
    onError: (error: Error, variables: FormData) => {
      console.error(`[user_services] useSubmitVerificationForUser error: ${error}`);
      // Token handling is now managed by axios interceptor
    }
  });
};

// Fetch Verification Details
export const useFetchVerificationDetails = (requestParams: VerificationsDetailsPullRequest) => {
  const queryClient = useQueryClient();
  const { reqID } = requestParams;
  const queryKeyConst = ['verificationDetails', reqID] as const;

  const queryResult = useQuery<VerificationPayload, Error, VerificationPayload, typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      if (!reqID) throw new Error('[useFetchVerificationDetails] reqID is required.');
      try {
        const response = await axiosInstance.request<VerificationPayload>({
          url: ApiConfig.user.verifications_details_pull.endpoint.replace('{reqID}', reqID),
          method: ApiConfig.user.verifications_details_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!reqID,
    // onError: (error: Error) => { // Deprecated
    //     userVerificationDetailsStore.getState().setError(error as any);
    //     handleApiError(error, queryClient, 'user_services (useFetchVerificationDetails)', requestParams, queryKeyConst);
    // }
  });

  useEffect(() => {
    const store = userVerificationDetailsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      userVerificationDetailsStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setVerificationDetails(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, reqID, queryClient, requestParams]); // Added requestParams

  return queryResult;
};

// Fetch User's Volunteer Organization Qualifications
export const useFetchUserVolunteerQualifications = (requestParams?: VolunteerOrganizationQualificationsPullRequest, enabled: boolean = true) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['userVolunteerQualifications', requestParams] as const;

  // Check if user is authenticated before making request
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  const queryResult = useQuery<VolunteerQualificationPayload[], Error, VolunteerQualificationPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        const response = await axiosInstance.request<VolunteerQualificationPayload[]>({
          url: ApiConfig.user.volunteer_organization_qualifications_pull.endpoint,
          method: ApiConfig.user.volunteer_organization_qualifications_pull.method,
          headers: { 'Content-Type': 'application/json' },
        });
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    enabled: isAuthenticated && enabled, // Only fetch when user is authenticated and enabled
    // onError: (error: Error) => { // Deprecated
    //     userVolunteerQualificationsStore.getState().setError(error as any);
    //     handleApiError(error, queryClient, 'user_services (useFetchUserVolunteerQualifications)', requestParams, queryKeyConst);
    // }
  });

  useEffect(() => {
    const store = userVolunteerQualificationsStore.getState();
    if (queryResult.isLoading) {
      store.setIsFetching(true);
    } else if (queryResult.isError && queryResult.error) {
      userVolunteerQualificationsStore.getState().setError(queryResult.error as any);
      // Token handling is now managed by axios interceptor
    } else if (queryResult.isSuccess && queryResult.data) {
      store.setVolunteerQualifications(queryResult.data);
      store.setIsFetching(false);
      store.setError(null);
    }
  }, [queryResult.isLoading, queryResult.isError, queryResult.isSuccess, queryResult.data, queryResult.error, requestParams, queryClient]);

  return queryResult;
};

// Fetch User's Volunteer Organization Qualifications for Volunteer Assistance (by user ID)
export const useFetchUserVolunteerQualificationsByUserId = (userId: string, enabled: boolean = true) => {
  const queryClient = useQueryClient();
  const queryKeyConst = ['userVolunteerQualificationsByUserId', userId] as const;

  // Check if user is authenticated before making request
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  const queryResult = useQuery<VolunteerQualificationPayload[], Error, VolunteerQualificationPayload[], typeof queryKeyConst>({
    queryKey: queryKeyConst,
    queryFn: async () => {
      try {
        // Use admin endpoint to fetch user volunteer qualifications by user ID
        // This requires volunteer/admin permissions
        const response = await axiosInstance.request<VolunteerQualificationPayload[]>({
          url: `${API_BASE_URL}/admin/volunteer-qualifications`,
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          params: {
            user_id: userId,
          },
        });
        return response.data;
      } catch (error) {
        console.error('[useFetchUserVolunteerQualificationsByUserId] Error fetching user volunteer qualifications:', error);
        throw error;
      }
    },
    enabled: isAuthenticated && enabled && !!userId, // Only fetch when user is authenticated, enabled, and userId is provided
  });

  return queryResult;
};