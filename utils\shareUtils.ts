import { Platform } from 'react-native';
// import { formatDate } from './dateFormatter';
import { parseISO, format } from 'date-fns';

interface ShareEventProps {
    event: {
        id: string;
        title: string;
        date: string; // Start time
        endDate?: string; // End time for time period
        location: string;
        price?: number; // Event price
        mapLink?: string; // Google Maps link
    };
    t: (key: string, options?: any) => string;
    language: string;
}

interface SharePostProps {
    post: {
        id: string;
        title: string;
        updatedAt: string;
        author: string;
    };
    t: (key: string, options?: any) => string;
    language: string;
}

export const generateShareContent = ({ event, t, language }: ShareEventProps) => {
    // Create webapp link for 'open in webapp' option
    const webappLink = `https://haytech.io/events/${event.id}`;
    
    // Create app store links
    const storeLink = Platform.OS === 'ios' 
        ? t('events.share.appStoreLink')
        : t('events.share.playStoreLink');

    // Format date and time period
    const startDate = format(parseISO(event.date), 'PPP');
    const startTime = format(parseISO(event.date), 'HH:mm');
    let timeDisplay = `📅 ${startDate}\n🕐 ${startTime}`;
    
    if (event.endDate) {
        const endTime = format(parseISO(event.endDate), 'HH:mm');
        timeDisplay = `📅 ${startDate}\n🕐 ${startTime} - ${endTime}`;
    }

    // Format price
    const priceDisplay = event.price !== undefined 
        ? `💰 ${event.price === 0 ? t('events.share.free') : `HK$${event.price}`}`
        : '';

    const messageLines = [
        event.title,
        '',
        timeDisplay,
        `📍 ${event.location}`,
    ];

    // Add price if available
    if (priceDisplay) {
        messageLines.push('', priceDisplay);
    }

    // Add map link if available
    if (event.mapLink) {
        messageLines.push('', `🗺️ ${t('events.share.viewOnMap')}`, event.mapLink);
    }

    // Add webapp link
    messageLines.push('', `🌐 ${t('events.share.openInWebapp')}`, webappLink);

    // Add app download link
    messageLines.push('', `📱 ${t('events.share.appNotInstalled')}`, storeLink);

    const message = messageLines.join('\n');

    return {
        message,
        title: event.title,
    };
};

export const generatePostShareContent = ({ post, t, language }: SharePostProps) => {
    // Create webapp link for 'open in webapp' option
    const webappLink = `https://haytech.io/posts/${post.id}`;
    
    // Create app store links
    const storeLink = Platform.OS === 'ios' 
        ? t('posts.share.appStoreLink')
        : t('posts.share.playStoreLink');

    // Format date
    const publishDate = format(parseISO(post.updatedAt), 'PPP');
    const publishTime = format(parseISO(post.updatedAt), 'HH:mm');

    const messageLines = [
        post.title,
        '',
        `📅 ${publishDate}`,
        `🕐 ${publishTime}`,
        `✍️ ${t('posts.share.author')}: ${post.author}`,
        '',
        `🌐 ${t('posts.share.openInWebapp')}`,
        webappLink,
        '',
        `📱 ${t('posts.share.appNotInstalled')}`,
        storeLink
    ];

    const message = messageLines.join('\n');

    return {
        message,
        title: post.title
    };
};