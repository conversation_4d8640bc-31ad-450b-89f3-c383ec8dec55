import React, { useEffect, useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Dimensions,
  Platform,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { Text, MD3Theme } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { LineChart } from 'react-native-chart-kit';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter, Stack } from 'expo-router';
import { appStyleStore } from 'stores/app_style_store';
import { useFetchUserStatistics } from '@/api/user_services';
import { MonthlyAttendedEventPayload, TopAttendedEventPayload, StatisticsPullResponse } from '@/api/api_config';
import { ErrorView } from '@/common_modules/ErrorView';

// Define local interfaces to match what the component expects
interface Category {
  name: string;
  count: number;
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
}

// MonthlyEvent is already defined as MonthlyAttendedEventPayload, we can use that or keep this for clarity if needed.
// interface MonthlyEvent {
//   month: string;
//   count: number;
// }

// APIUserStats can be largely derived from StatisticsPullResponse, with the addition of joinedDays
interface APIUserStats extends Omit<StatisticsPullResponse, 'monthlyAttendedEvents' | 'topAttendedEventTags'> {
  joinedDays: number;
  totalHours: number; // This was manually added as 0, confirm if it should always be part of this transformed type
  monthlyEvents: MonthlyAttendedEventPayload[];
  categories: Category[];
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const CategoryItem = ({ icon, name, count, maxCount, t }: {
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
  name: string;
  count: number;
  maxCount: number;
  t: (key: string) => string;
}) => {
  const theme = appStyleStore(s => s.theme) as MD3Theme;
  const styles = getStyles(theme);

  return (
    <View style={styles.categoryItem}>
      <View style={styles.categoryIcon}>
        <MaterialCommunityIcons name={icon} size={24} color={theme.system.secondaryText} />
      </View>
      <View style={styles.categoryInfo}>
        <Text style={[styles.categoryName, { color: theme.system.text }]}>{t(`category.${name}`)}</Text>
        <View style={[styles.categoryBar, { backgroundColor: theme.system.border }]}>
          <View style={[styles.categoryBarFill, { width: `${(count / maxCount) * 100}%`, backgroundColor: theme.colors.primary }]} />
        </View>
      </View>
      <Text style={[styles.categoryCount, { color: theme.system.secondaryText }]}>{count}</Text>
    </View>
  );
};

const generatePastSixMonths = (): MonthlyAttendedEventPayload[] => {
  const months: MonthlyAttendedEventPayload[] = [];
  const today = new Date();
  for (let i = 5; i >= 0; i--) {
    const date = new Date(today);
    date.setMonth(today.getMonth() - i);
    const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    months.push({
      month,
      count: 0
    });
  }
  return months;
};

export function StatsScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(s => s.theme) as MD3Theme;
  const router = useRouter();
  
  // Use the hook for data fetching
  const { data: rawStatsData, isLoading, error: fetchError, refetch } = useFetchUserStatistics();

  const [transformedStats, setTransformedStats] = useState<APIUserStats | null>(null);

  useEffect(() => {
    if (rawStatsData) {
      const pastSixMonths = generatePastSixMonths();
      const monthlyEvents = pastSixMonths.map(defaultMonth => {
        const matchingServerData = rawStatsData.monthlyAttendedEvents.find(
          (serverMonth: MonthlyAttendedEventPayload) => serverMonth.month === defaultMonth.month
        );
        return {
          month: defaultMonth.month,
          count: matchingServerData ? matchingServerData.count : 0
        };
      });

      const joinedDate = new Date(rawStatsData.userJoinedAt);
      const today = new Date();
      const diffTime = Math.abs(today.getTime() - joinedDate.getTime());
      const joinedDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      const transformedData: APIUserStats = {
        totalEvents: rawStatsData.totalEvents,
        userJoinedAt: rawStatsData.userJoinedAt,
        volunteerEvents: rawStatsData.volunteerEvents,
        joinedDays: joinedDays,
        totalHours: 0, // As per original logic
        monthlyEvents: monthlyEvents,
        categories: rawStatsData.topAttendedEventTags.map((tag: TopAttendedEventPayload) => ({
          name: tag.name,
          count: tag.count,
          icon: 'tag' as keyof typeof MaterialCommunityIcons.glyphMap
        }))
      };
      setTransformedStats(transformedData);
    }
  }, [rawStatsData]);

  // Ensure theme is loaded
  if (!theme) {
    return (
      <>
        <Stack.Screen options={{ headerTitle: t('profile.stats.title') }} />
        <View style={[getStyles({} as MD3Theme).container, getStyles({} as MD3Theme).centerContent]}> 
          <ActivityIndicator size="large" />
        </View>
      </>
    );
  }
  const styles = getStyles(theme);

  if (isLoading) {
    return (
      <>
        <Stack.Screen options={{ headerTitle: t('profile.stats.title') }} />
        <View style={[styles.container, styles.centerContent]}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      </>
    );
  }

  if (fetchError || !transformedStats) {
    return (
      <>
        <Stack.Screen options={{ headerTitle: t('profile.stats.title') }} />
        <ErrorView onRetry={refetch} />
      </>
    );
  }

  const maxValue = Math.max(1, ...transformedStats.monthlyEvents.map(event => event.count));
  const calculateSegments = (max: number) => {
    if (max <= 5) return max;
    const idealSegments = 5;
    const step = Math.ceil(max / idealSegments);
    return Math.ceil(max / step);
  };
  const segments = calculateSegments(maxValue);

  const monthlyChartConfig = {
    backgroundGradientFrom: theme.colors.background,
    backgroundGradientTo: theme.colors.background,
    decimalPlaces: 0,
    color: (opacity = 1) => theme.colors.primaryLight,
    labelColor: (opacity = 1) => theme.system.secondaryText,
    style: {
      borderRadius: 16,
    },
    propsForBackgroundLines: {
      strokeDasharray: '',
      strokeWidth: 1,
      stroke: theme.system.border,
    },
    propsForLabels: {
      fontSize: 13,
      fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
    },
    yAxisMinValue: 0,
    yAxisMaxValue: maxValue,
    formatYLabel: (value: string) => {
      const num = Math.round(Number(value));
      return num.toString();
    },
  };

  const formatMonthLabel = (monthStr: string) => {
    const parts = monthStr.split('-');
    if (parts.length !== 2) return monthStr;
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const monthIndex = parseInt(parts[1], 10) - 1;
    return monthNames[monthIndex] || monthStr;
  };

  const monthlyData = {
    labels: transformedStats.monthlyEvents.map(event => formatMonthLabel(event.month)),
    datasets: [{
      data: transformedStats.monthlyEvents.map(event => event.count),
      color: (opacity = 1) => theme.colors.primaryLight,
      strokeWidth: 2,
    }]
  };

  const maxCategoryCount = Math.max(1, ...transformedStats.categories.map(category => category.count));

  return (
    <>
      <Stack.Screen options={{ headerTitle: t('profile.stats.title') }} />
      <ScrollView style={[styles.container, { backgroundColor: theme.system.background }]} showsVerticalScrollIndicator={false}>
        <LinearGradient
          colors={[`${theme.colors.primary}15`, `${theme.colors.primary}05`]}
          style={styles.headerSection}
        >
          <View style={styles.totalEventsInfo}>
            <Text style={[styles.totalEventsText, { color: theme.system.text }]}>
              {transformedStats.totalEvents}
            </Text>
            <Text style={[styles.totalEventsLabel, { color: theme.system.secondaryText }]}>
              {t('profile.stats.overview.totalEvents')}
            </Text>
          </View>

          <View style={[styles.statsRow, { backgroundColor: '#FFFFFF' }]}>
            <View style={styles.statItem}>
              <MaterialCommunityIcons name="calendar-month" size={24} color={theme.system.secondaryText} />
              <Text style={[styles.statValue, { color: theme.system.text }]}>
                {transformedStats.joinedDays}<Text style={[styles.statUnit, { color: theme.system.secondaryText }]}>d</Text>
              </Text>
              <Text style={[styles.statLabel, { color: theme.system.secondaryText }]}>{t('profile.stats.joinedDays')}</Text>
            </View>
            <View style={[styles.statDivider, { backgroundColor: theme.system.border }]} />
            <View style={styles.statItem}>
              <MaterialCommunityIcons name="clock-outline" size={24} color={theme.system.secondaryText} />
              <Text style={[styles.statValue, { color: theme.system.text }]}>
                {Math.round(transformedStats.totalHours)}<Text style={[styles.statUnit, { color: theme.system.secondaryText }]}>h</Text>
              </Text>
              <Text style={[styles.statLabel, { color: theme.system.secondaryText }]}>{t('profile.stats.overview.totalHours')}</Text>
            </View>
            <View style={[styles.statDivider, { backgroundColor: theme.system.border }]} />
            <View style={styles.statItem}>
              <MaterialCommunityIcons name="hand-heart" size={24} color={theme.system.secondaryText} />
              <Text style={[styles.statValue, { color: theme.system.text }]}>{transformedStats.volunteerEvents}</Text>
              <Text style={[styles.statLabel, { color: theme.system.secondaryText }]}>{t('profile.stats.overview.volunteerEvents')}</Text>
            </View>
          </View>
        </LinearGradient>

        <View style={styles.section}>
          <View style={styles.sectionTitleRow}>
            <Text style={[styles.sectionTitle, { color: theme.system.text }]}>
              {t('profile.stats.details.monthly.title')}
            </Text>
            <Text style={[styles.monthlyChartNote, { color: theme.system.secondaryText }]}>
              ({t('profile.stats.details.monthly.sixMonthsNote')})
            </Text>
          </View>
          <LineChart
            data={monthlyData}
            width={SCREEN_WIDTH - 40} 
            height={220}
            chartConfig={monthlyChartConfig}
            bezier
            style={styles.chart}
            withVerticalLines={false}
            withHorizontalLines={true}
            withVerticalLabels={true}
            withHorizontalLabels={true}
            segments={segments}
            getDotColor={() => theme.colors.primary}
            renderDotContent={({ x, y, index, indexData }) => {
              if (indexData === 0) return null;
              const adjustedY = Platform.select({
                ios: y - 18,
                android: y - 220 - 18, 
              });
              return (
                <Text
                  key={index}
                  style={[
                    styles.dotLabel,
                    {
                      position: 'absolute',
                      left: x - 10,
                      top: adjustedY,
                      zIndex: 1,
                      color: theme.system.secondaryText,
                    }
                  ]}
                >
                  {indexData}
                </Text>
              );
            }}
          />
        </View>

        <View style={styles.section}>
          <View style={styles.sectionTitleRow}>
            <Text style={[styles.sectionTitle, { color: theme.system.text }]}>{t('category.title')}</Text>
            <TouchableOpacity
              style={styles.viewAllButton}
              onPress={() => router.push({ pathname: '/tabs/myEvents', params: { dateFilter: 'past', typeFilter: 'all' } })}
            >
              <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>{t('profile.stats.details.categories.viewAll')}</Text>
              <MaterialCommunityIcons name="chevron-right" size={20} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>
          {transformedStats.categories && transformedStats.categories.length > 0 ?
            (
              transformedStats.categories.map((category) => (
                <CategoryItem
                  key={category.name}
                  icon={category.icon}
                  name={category.name}
                  count={category.count}
                  maxCount={maxCategoryCount}
                  t={t}
                />
              ))
            ) : (
              <View style={styles.emptyDataSection}>
                <MaterialCommunityIcons name="tag-multiple" size={48} color={theme.system.secondaryText} />
                <Text style={[styles.noDataText, { color: theme.system.secondaryText }]}>
                  {t('profile.stats.details.categories.noData')}
                </Text>
              </View>
            )}
        </View>
      </ScrollView>
    </>
  );
}

const getStyles = (theme: MD3Theme) => StyleSheet.create({
  container: {
    flex: 1,
  },
  headerSection: {
    paddingTop: 32,
    paddingBottom: 24,
    paddingHorizontal: 16,
  },
  totalEventsInfo: {
    alignItems: 'center',
    marginBottom: 24,
  },
  totalEventsText: {
    fontSize: 40,
    fontWeight: Platform.OS === 'ios' ? '700' : 'bold',
    marginBottom: 4,
  },
  totalEventsLabel: {
    fontSize: 15,
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
  },
  statsRow: {
    flexDirection: 'row',
    borderRadius: 16,
    padding: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    width: 1,
    marginHorizontal: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: Platform.OS === 'ios' ? '700' : 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  statUnit: {
    fontSize: 16,
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
  },
  statLabel: {
    fontSize: 13,
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
    textAlign: 'center',
  },
  section: {
    padding: 16,
    alignItems: 'center',
  },
  sectionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
    alignSelf: 'stretch',
    width: '100%',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: Platform.OS === 'ios' ? '700' : 'bold',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
    marginRight: 4,
  },
  chart: {
    marginVertical: 4,
    borderRadius: 16,
    alignSelf: 'center',
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
    marginRight: 12,
  },
  categoryName: {
    fontSize: 15,
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
    marginBottom: 6,
  },
  categoryBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  categoryBarFill: {
    height: '100%',
    borderRadius: 2,
  },
  categoryCount: {
    fontSize: 15,
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
    minWidth: 24,
    textAlign: 'right',
  },
  dotLabel: {
    fontSize: 11,
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
    textAlign: 'center',
    width: 20,
    backgroundColor: 'transparent',
    textShadowColor: 'rgba(255, 255, 255, 0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  monthlyChartNote: {
    fontSize: 13,
    marginLeft: 8,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    padding: 12,
  },
  retryText: {
    fontSize: 16,
    fontWeight: '600',
  },
  noDataText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 8,
  },
  noDataContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  emptyDataSection: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 150,
    marginHorizontal: 16,
  },
});

export default StatsScreen;
