/**
 * PKCE (Proof Key for Code Exchange) Utilities
 * 
 * This utility provides functions for generating PKCE credentials
 * as specified in RFC 7636, compatible with React Native.
 */

// Use expo-crypto for all crypto operations
import * as Crypto from 'expo-crypto';

/**
 * Generates a cryptographically random string for use as a PKCE code verifier.
 * 
 * @returns A random string between 43 and 128 characters long.
 */
async function generateCodeVerifier(): Promise<string> {
  // Generate a random string of 43-128 chars as per PKCE spec
  // For consistency, we'll use a fixed length of 43 characters
  const length = 43;
  const randomBytes = await Crypto.getRandomBytesAsync(32); // 32 bytes gives good randomness
  
  // Convert to Base64URL format (RFC 4648 §5)
  const encoder = new TextEncoder();
  const verifier = base64UrlEncode(randomBytes);
  
  console.log('Generated code verifier of length:', verifier.length);
  return verifier;
}

/**
 * Generates a random state value for CSRF protection.
 * 
 * @returns A random string to be used as the state parameter.
 */
async function generateState(): Promise<string> {
  const randomBytes = await Crypto.getRandomBytesAsync(32); // 32 bytes should be sufficient
  const state = base64UrlEncode(randomBytes);
  console.log('Generated state of length:', state.length);
  return state;
}

/**
 * Creates a code challenge from a code verifier using SHA-256.
 * 
 * @param codeVerifier The code verifier string
 * @returns The code challenge derived from the code verifier
 */
async function generateCodeChallenge(codeVerifier: string): Promise<string> {
  console.log('Generating code challenge from verifier:', codeVerifier);
  
  // 1. Hash using SHA-256 to get binary digest
  const digestHex = await Crypto.digestStringAsync(
    Crypto.CryptoDigestAlgorithm.SHA256,
    codeVerifier
  );
  
  console.log('SHA-256 digest (hex):', digestHex);
  
  // 2. Convert hex digest to binary
  const digestBinary = hexToArrayBuffer(digestHex);
  
  // 3. Convert binary to base64url
  const codeChallenge = base64UrlEncode(new Uint8Array(digestBinary));
  
  console.log('Code challenge result:', codeChallenge);
  console.log('Code challenge length:', codeChallenge.length);
  
  return codeChallenge;
}

/**
 * Converts a hex string to an ArrayBuffer
 * 
 * @param hexString A hex-encoded string
 * @returns An ArrayBuffer containing the binary data
 */
function hexToArrayBuffer(hexString: string): ArrayBuffer {
  const bytes = new Uint8Array(hexString.length / 2);
  for (let i = 0; i < hexString.length; i += 2) {
    bytes[i / 2] = parseInt(hexString.substring(i, i + 2), 16);
  }
  return bytes.buffer;
}

/**
 * Encodes a Uint8Array as a Base64URL string.
 * 
 * @param buffer The buffer to encode
 * @returns A Base64URL encoded string (without padding)
 */
function base64UrlEncode(buffer: Uint8Array): string {
  // Convert the buffer to a base64 string
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  
  // Standard base64 encoding
  const base64 = btoa(binary);
  
  // Convert to base64url format as specified in RFC 7636:
  // - Replace "+" with "-"
  // - Replace "/" with "_"
  // - Remove "=" padding
  const base64url = base64
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
  
  return base64url;
}

/**
 * Type definition for PKCE credentials
 */
export interface PKCECredentials {
  codeVerifier: string;
  codeChallenge: string;
  state: string;
}

/**
 * Generates all required PKCE parameters for an authentication flow.
 * 
 * @returns A promise resolving to an object containing codeVerifier, codeChallenge, and state
 */
export async function generatePKCE(): Promise<PKCECredentials> {
  console.log('Generating PKCE credentials...');
  
  const codeVerifier = await generateCodeVerifier();
  const codeChallenge = await generateCodeChallenge(codeVerifier);
  const state = await generateState();
  
  console.log('PKCE generation complete');
  console.log('Verifier length:', codeVerifier.length);
  console.log('Challenge length:', codeChallenge.length);
  console.log('State length:', state.length);
  
  return {
    codeVerifier,
    codeChallenge,
    state
  };
} 