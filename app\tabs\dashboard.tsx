import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform,
  Image,
  Dimensions,
  Animated,
  Modal,
  TouchableWithoutFeedback,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Card } from 'react-native-paper';
import { AuthModal } from '@/common_modules/AuthModal';
import type {
  PostListPayload,
  EventListPayload,
  OrganizationListPayload,
  PublishedEventListPullRequest,
  PublishedPostsListPullRequest,
} from '@/api/api_config';
import { dashboardEventsListStore } from 'stores/public_events_store';
import { postsDashboardListStore } from 'stores/posts_store';
import { organizationListStore, organizationStore } from 'stores/organization_store';
import { createTheme } from 'theme/index';
import { useRouter } from 'expo-router';
import { authenticationStore } from 'stores/authentication_store';
import { appStyleStore } from 'stores/app_style_store';

// --- Import API service hooks ---
import { useFetchDashboardEvents } from '@/api/public_events_services';
import { useFetchDashboardPosts } from '@/api/posts_services';
import { useFetchOrganizationList } from '@/api/organization_services';
import { useFetchUserOrganizations } from '@/api/user_services';
import { useFetchUserProfile } from '@/api/user_services';
import { userOrganizationsStore } from 'stores/user_store';
import { userProfileStore } from 'stores/user_store';
import { format, parseISO, startOfDay as dfnsStartOfDay, addDays as dfnsAddDays, endOfDay as dfnsEndOfDay, subDays as dfnsSubDays, Locale } from 'date-fns';
import { enUS, zhCN } from 'date-fns/locale';
import { fromZonedTime, toZonedTime } from 'date-fns-tz';
import { getValidImageUrl, processEventImageUrls } from '@/utils/imageUtils';

const default_organization_logo = require('@/assets/default-images/default-logo.png');

type Organization = OrganizationListPayload;

// Default organization if stores are empty initially
const defaultOrganizationData: Organization = {
    id: null,
    name: 'Welcome',
    image_url: default_organization_logo,
    description: 'Please select an organization.',
    theme_color: 'blue',
    owner_user_id: 'system',
    is_default_org: true,
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
};

// Add locale and date formatting functions
const getLocale = (language: string): Locale => {
  switch (language.toLowerCase().split('-')[0]) {
    case 'zh':
      return zhCN;
    default:
      return enUS;
  }
};

const formatPostDate = (dateString: string | undefined, language: string): string => {
  if (!dateString) return '';
  try {
    const timeZone = 'Asia/Hong_Kong';
    const parsedDate = parseISO(dateString);
    const zonedDate = toZonedTime(parsedDate, timeZone);
    return format(zonedDate, 'yyyy-MM-dd', { locale: getLocale(language) });
  } catch (error) {
    console.warn('Error formatting post date:', error);
    return '';
  }
};

const formatEventDateTime = (dateString: string | undefined, language: string): string => {
  if (!dateString) return '';
  try {
    const timeZone = 'Asia/Hong_Kong';
    const parsedDate = parseISO(dateString);
    const zonedDate = toZonedTime(parsedDate, timeZone);
    return format(zonedDate, 'yyyy-MM-dd HH:mm', { locale: getLocale(language) });
  } catch (error) {
    console.warn('Error formatting event date:', error);
    return '';
  }
};

// Define generateStyles function at module level
const generateStyles = (theme: ReturnType<typeof createTheme>, cardWidth: number, cardHeight: number) => StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  section: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.system.text,
  },
  viewAllButton: {
    fontSize: 14,
    color: theme.colors.primary,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    rowGap: 16,
    columnGap: 0,
    marginHorizontal: -8,
  },
  featureButton: {
    width: '25%',
    alignItems: 'center' as 'center',
    justifyContent: 'flex-start' as 'flex-start',
    paddingHorizontal: 8,
  },
  featureIconContainer: {
    width: 46,
    height: 46,
    borderRadius: 14,
    alignItems: 'center' as 'center',
    justifyContent: 'center' as 'center',
    marginBottom: 8,
  },
  featureLabel: {
    fontSize: 13,
    color: theme.system.secondaryText,
    textAlign: 'center' as 'center',
    lineHeight: 16,
    fontWeight: '500',
  },
  eventsList: {
    gap: 12,
  },
  eventCardContainer: {
    borderRadius: 12,
    marginHorizontal: Platform.OS === 'ios' ? 0.5 : 0,
    marginVertical: Platform.OS === 'ios' ? 0.5 : 0,
  },
  eventCard: {
    backgroundColor: theme.system.background,
    borderRadius: 12,
  },
  eventCardTouchable: {
    width: '100%',
    overflow: 'hidden',
    borderRadius: 12,
  },
  eventContent: {
    flexDirection: 'row',
    padding: 12,
  },
  eventImagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: theme.colors.primaryContainer,
    alignItems: 'center' as 'center',
    justifyContent: 'center' as 'center',
  },
  eventInfo: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'space-between' as 'space-between',
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.system.text,
    marginBottom: 4,
  },
  eventDetailsItem: {
    flexDirection: 'row',
    alignItems: 'center' as 'center',
    gap: 4,
    marginBottom: 2,
  },
  eventDetailsText: {
    fontSize: 14,
    color: theme.system.secondaryText,
    flex: 1,
  },
  welcomeHeader: {
    backgroundColor: theme.colors.background,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.system.border,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center' as 'center',
    gap: 16,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center' as 'center',
  },
  logo: {
    width: 56,
    height: 56,
  },
  logoChevron: {
    marginLeft: 4,
  },
  welcomeText: {
    flex: 1,
  },
  greeting: {
    fontSize: 24,
    fontWeight: '600',
    color: theme.system.text,
    marginBottom: 6,
  },
  welcomeSubtitle: {
    fontSize: 15,
    color: theme.system.secondaryText,
    lineHeight: 20,
  },
  emptyStateContainer: {
    alignItems: 'center' as 'center',
    justifyContent: 'center' as 'center',
    paddingVertical: 24,
    gap: 12,
  },
  errorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.system.text,
    textAlign: 'center' as 'center',
    marginBottom: 4,
  },
  errorText: {
    fontSize: 15,
    color: theme.system.secondaryText,
    textAlign: 'center' as 'center',
  },
  loadingContainer: {
    height: 100,
    alignItems: 'center' as 'center',
    justifyContent: 'center' as 'center',
  },
  noContentText: {
    fontSize: 15,
    color: theme.system.secondaryText,
    textAlign: 'center' as 'center',
  },
  newsCardContainer: {
    borderRadius: 12,
    marginHorizontal: Platform.OS === 'ios' ? 0.5 : 0,
    marginVertical: Platform.OS === 'ios' ? 0.5 : 0,
    width: cardWidth,
    height: cardHeight,
    marginRight: 12,
  },
  newsCard: {
    backgroundColor: theme.system.background,
    borderRadius: 12,
    height: cardHeight,
  },
  newsCardTouchable: {
    width: '100%',
    overflow: 'hidden',
    borderRadius: 12,
  },
  newsCardImage: {
    width: cardWidth,
    height: 140,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  newsCardContent: {
    padding: 12,
    height: 80,
    justifyContent: 'space-between' as 'space-between',
  },
  newsCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.system.text,
    height: 42,
  },
  newsCardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between' as 'space-between',
    alignItems: 'center' as 'center',
  },
  newsCardAuthor: {
    flexDirection: 'row',
    alignItems: 'center' as 'center',
  },
  newsCardDate: {
    flexDirection: 'row',
    alignItems: 'center' as 'center',
  },
  newsCardIcon: {
    marginRight: 4,
  },
  newsCardMeta: {
    fontSize: 14,
    color: theme.system.secondaryText,
  },
  orgSwitchOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end' as 'flex-end',
  },
  orgSwitchContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    paddingTop: 12,
    paddingBottom: Platform.OS === 'ios' ? 34 : 24,
  },
  orgSwitchHandle: {
    width: 36,
    height: 4,
    backgroundColor: theme.system.border,
    borderRadius: 2,
    alignSelf: 'center' as 'center',
    marginBottom: 16,
  },
  orgSwitchTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.system.text,
    marginBottom: 12,
    marginLeft: 12,
  },
  orgSwitchOptionsContainer: {
    marginBottom: 24,
    maxHeight: Dimensions.get('window').height * 0.5,
  },
  orgSwitchOptionsContent: {
    paddingHorizontal: 12,
  },
  orgSwitchOption: {
    flexDirection: 'row',
    alignItems: 'center' as 'center',
    height: 56,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  orgSwitchOptionText: {
    fontSize: 16,
    flex: 1,
  },
  orgSwitchSelectedText: {
    fontWeight: '600',
  },
  orgSwitchConfirmButton: {
    height: 48,
    borderRadius: 24,
    alignItems: 'center' as 'center',
    justifyContent: 'center' as 'center',
    marginHorizontal: 12,
  },
  orgSwitchConfirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  orgSwitchOrgContent: {
    flexDirection: 'row',
    alignItems: 'center' as 'center',
    flex: 1,
  },
  orgSwitchOrgLogo: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12,
  },
});

export default function DashboardScreen() {
  // console.log('DashboardScreen');
  const { t, i18n } = useTranslation();
  const router = useRouter();

  // --- Authentication State ---
  const [showAuthModal, setShowAuthModal] = React.useState(false);
  const [pendingAction, setPendingAction] = React.useState<(() => void) | null>(null);
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);

  // --- Use global theme (no more local theme management) ---
  const theme = appStyleStore(state => state.theme);
  const currentThemeName = appStyleStore(state => state.currentThemeName);

  // Memoize theme to prevent unnecessary re-renders when theme object reference changes
  const memoizedTheme = React.useMemo(() => theme, [currentThemeName]);

  // --- Fetch User Profile for language preference ---
  useFetchUserProfile();
  const userProfileFromStore = userProfileStore(state => state.profile);

  // --- Language Setting Logic (Only for authenticated users with profile preferences) ---
  React.useEffect(() => {
    if (isAuthenticated && userProfileFromStore && userProfileFromStore.interface_language) {
      if (i18n.language !== userProfileFromStore.interface_language) {
        console.log(`[Dashboard] Setting language from user profile: ${userProfileFromStore.interface_language}`);
        i18n.changeLanguage(userProfileFromStore.interface_language);
      }
    }
  }, [isAuthenticated, userProfileFromStore, i18n]);

  // --- Data from Zustand Stores (Simplified Organization Logic) ---
  const posts = postsDashboardListStore(state => state.postsList) || [];
  const isFetchingPosts = postsDashboardListStore(state => state.isFetching);
  const postsError = postsDashboardListStore(state => state.error);

  const upcomingEvents = dashboardEventsListStore(state => state.dashboardEventsList) || [];
  const isFetchingEvents = dashboardEventsListStore(state => state.isFetching);
  const eventsError = dashboardEventsListStore(state => state.error);
  
  // Simplified organization data management
  const selectedOrganization = organizationStore(state => state.selectedOrganization);
  const setSelectedOrganization = organizationStore(state => state.setSelectedOrganization);
  const setSelectedOrganizationWithTheme = organizationStore(state => state.setSelectedOrganizationWithTheme);
  
  // Get organization lists based on authentication
  const publicOrganizations = organizationListStore(state => state.organizationList);
  const publicOrgsLoading = organizationListStore(state => state.isFetching);
  const publicOrgsError = organizationListStore(state => state.error);
  
  const userOrganizations = userOrganizationsStore(state => state.userOrganizations);
  const userOrgsLoading = userOrganizationsStore(state => state.isFetching);
  const userOrgsError = userOrganizationsStore(state => state.error);

  // Determine current data source based on authentication
  const availableOrganizations = isAuthenticated ? userOrganizations : publicOrganizations;
  const isLoadingOrganizations = isAuthenticated ? userOrgsLoading : publicOrgsLoading;
  const organizationsError = isAuthenticated ? userOrgsError : publicOrgsError;

  // Clean up invalid selectedOrganization when availableOrganizations changes
  const organizationCleanupRef = React.useRef<string | null>(null);

  React.useEffect(() => {
    // Prevent infinite loops by tracking the last processed organization ID
    const currentOrgId = selectedOrganization?.id || null;

    // Skip if we've already processed this organization
    if (organizationCleanupRef.current === currentOrgId) {
      return;
    }

    // Only run cleanup if we have both selectedOrganization and availableOrganizations
    if (selectedOrganization && availableOrganizations && availableOrganizations.length > 0) {
      // Check if the currently selected organization still exists in available organizations
      const isSelectedOrgStillAvailable = availableOrganizations.some(
        org => org.id === selectedOrganization.id
      );

      if (!isSelectedOrgStillAvailable) {
        console.log(`[Dashboard] Selected organization (${selectedOrganization.name || selectedOrganization.id}) no longer available in current organization list, clearing selection`);
        organizationCleanupRef.current = null; // Mark as cleared
        setSelectedOrganization(null);
        return;
      }
    }
    // Handle case where availableOrganizations becomes empty
    else if (selectedOrganization && availableOrganizations && availableOrganizations.length === 0) {
      console.log(`[Dashboard] No organizations available, clearing selected organization (${selectedOrganization.name || selectedOrganization.id})`);
      organizationCleanupRef.current = null; // Mark as cleared
      setSelectedOrganization(null);
      return;
    }

    // Update the ref to track current organization
    organizationCleanupRef.current = currentOrgId;
  }, [selectedOrganization?.id, availableOrganizations?.length, setSelectedOrganization]);

  // Determine current organization with fallback logic
  const currentOrganization = React.useMemo(() => {
    if (selectedOrganization) {
      return selectedOrganization;
    }
    if (availableOrganizations && availableOrganizations.length > 0) {
      return availableOrganizations[0];
    }
    return defaultOrganizationData;
  }, [selectedOrganization, availableOrganizations]);

  const [showOrgModal, setShowOrgModal] = React.useState(false);

  // --- API Calls ---
  const publicOrgListQuery = useFetchOrganizationList({ enabled: !isAuthenticated });
  const userOrgListQuery = useFetchUserOrganizations(undefined, { enabled: isAuthenticated });

  const validOrgId = React.useMemo(() => {
    return currentOrganization?.id || undefined;
  }, [currentOrganization]);

  const timeZone = 'Asia/Hong_Kong';
  const now = toZonedTime(new Date(), timeZone);
  const eventsStartDateISO = fromZonedTime(now, timeZone).toISOString();
  // Calculate end date (7 days from now)
  const eventsEndDateISO = fromZonedTime(toZonedTime(dfnsAddDays(dfnsEndOfDay(fromZonedTime(now, timeZone)), 7), timeZone), timeZone).toISOString();
  // Calculate end date (30 days before)
  const postsStartDateISO = fromZonedTime(toZonedTime(dfnsSubDays(dfnsStartOfDay(fromZonedTime(now, timeZone)), 30), timeZone), timeZone).toISOString();
  const postsEndDateISO = fromZonedTime(now, timeZone).toISOString();

  const dashboardEventParams = React.useMemo<PublishedEventListPullRequest | undefined>(() => {
    return validOrgId
      ? { org_id: validOrgId, status: 'published', start_date: eventsStartDateISO, end_date: eventsEndDateISO, limit: 10 }
      : { status: 'published', start_date: eventsStartDateISO, end_date: eventsEndDateISO, limit: 10 };
  }, [validOrgId]);

  const publishedPostsParams = React.useMemo<PublishedPostsListPullRequest | undefined>(() => {
    return validOrgId
      ? { org_id: validOrgId, statuses: ['published'], start_date: postsStartDateISO, end_date: postsEndDateISO, limit: 10, sort_by: 'updated_at', sort_order: 'desc' }
      : { statuses: ['published'], start_date: postsStartDateISO, end_date: postsEndDateISO, limit: 10, sort_by: 'updated_at', sort_order: 'desc' };
  }, [validOrgId]);

  const eventsQuery = useFetchDashboardEvents(dashboardEventParams);
  const postsQuery = useFetchDashboardPosts(publishedPostsParams);

  // --- Refresh Control State and Handler ---
  const [refreshing, setRefreshing] = React.useState(false);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    try {
      // Refetch all data in parallel
      const refreshPromises: Promise<any>[] = [
        eventsQuery.refetch(),
        postsQuery.refetch(),
      ];
      if (isAuthenticated) {
        refreshPromises.push(userOrgListQuery.refetch());
      } else {
        refreshPromises.push(publicOrgListQuery.refetch());
      }
      await Promise.all(refreshPromises);
    } catch (error) {
      console.error("Error during refresh:", error);
    } finally {
      setRefreshing(false);
    }
  }, [eventsQuery, postsQuery, isAuthenticated, publicOrgListQuery, userOrgListQuery]);

  // Constants for styles that depend on dimensions, calculated once or via useWindowDimensions if dynamic
  const screenWidth = Dimensions.get('window').width;
  const cardWidth = Platform.OS === 'web' && screenWidth > 768 ? 320 : 280;
  const cardHeight = 220;

  // Memoize styles, pass theme and card dimensions
  const styles = React.useMemo(() => generateStyles(memoizedTheme, cardWidth, cardHeight), [memoizedTheme, cardWidth, cardHeight]);

  // --- Inlined OrgSwitch Logic ---
  const [selectedOrgIdForSwitch, setSelectedOrgIdForSwitch] = React.useState<string>(
    currentOrganization?.id || ''
  );
  const [orgSwitchAnimation] = React.useState(new Animated.Value(0));


  React.useEffect(() => {
    if (showOrgModal && currentOrganization) {
      setSelectedOrgIdForSwitch(currentOrganization.id || '');
    }
  }, [showOrgModal, currentOrganization]);

  React.useEffect(() => {
    Animated.timing(orgSwitchAnimation, {
      toValue: showOrgModal ? 1 : 0,
      duration: showOrgModal ? 250 : 200,
      useNativeDriver: true,
    }).start();
  }, [showOrgModal]);

  const orgSwitchTranslateY = orgSwitchAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [Dimensions.get('window').height, 0],
  });

  const handleOrgSwitchClose = (selectedOrgFromSwitch?: Organization) => {
    Animated.timing(orgSwitchAnimation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setShowOrgModal(false);
      if (selectedOrgFromSwitch) {
        setSelectedOrganizationWithTheme(selectedOrgFromSwitch); // Update organization and theme safely
      }
    });
  };

  const handleOrgSwitchConfirm = () => {
    const newlySelectedOrg = availableOrganizations?.find(org => org.id === selectedOrgIdForSwitch);
    if (newlySelectedOrg && newlySelectedOrg.id !== currentOrganization?.id) {
      handleOrgSwitchClose(newlySelectedOrg);
    } else {
      handleOrgSwitchClose();
    }
  };

  const handleFeaturePress = (action: () => void, requiresAuthParam?: boolean) => {
    if (requiresAuthParam && !isAuthenticated) {
      setPendingAction(() => action);
      setShowAuthModal(true);
    } else {
      action();
    }
  };

  const handleLogin = () => {
    setShowAuthModal(false);
    router.push('/tabs/login');
  };

  const handleSearchPress = () => router.push('/tabs/explore/events?focusSearch=true');
  const handleViewAllEvents = () => router.push('/tabs/explore/events');
  const handleViewAllPosts = () => router.push('/tabs/explore/posts');

  interface FeatureButtonProps {
    icon: React.ComponentProps<typeof MaterialCommunityIcons>['name'];
    label: string;
    onPress: () => void;
    requiresAuth?: boolean;
  }

  const FeatureButton = ({ icon, label, onPress, requiresAuth = false }: FeatureButtonProps) => (
    <TouchableOpacity
      style={styles.featureButton}
      onPress={() => handleFeaturePress(onPress, requiresAuth)}
    >
      <View style={[styles.featureIconContainer, { backgroundColor: memoizedTheme.colors.primaryContainer }]}>
        <MaterialCommunityIcons name={icon} size={22} color={memoizedTheme.colors.primary} />
      </View>
      <Text style={styles.featureLabel}>{label}</Text>
    </TouchableOpacity>
  );

  const EventCard = ({ event }: { event: EventListPayload }) => {
    const [imageError, setImageError] = React.useState(false);
    const processedEvent = processEventImageUrls(event);
    
    // Try to get image from various sources
    const imageUrl = processedEvent.processed_image_url || 
                    (processedEvent.banner_image_urls && processedEvent.banner_image_urls[0]) ||
                    (processedEvent.media_items && processedEvent.media_items[0]?.file_path);
    
    let displayLocation = event.location_full_address || event.location_online_url || t('common.locationUnavailable');
    
    return (
      <View style={styles.eventCardContainer}>
        <Card style={[styles.eventCard, { backgroundColor: memoizedTheme.system.background }]} mode="elevated">
          <TouchableOpacity
            onPress={() => router.push({ pathname: '/explore/events/EventDetailsScreen', params: { eventId: event.id, orgId: event.organization_id } })}
            style={styles.eventCardTouchable}
            activeOpacity={0.7}
          >
            <View style={styles.eventContent}>
              {imageUrl && !imageError ? (
                <Image
                  source={{ uri: imageUrl }}
                  style={[styles.eventImagePlaceholder, { backgroundColor: 'transparent' }]}
                  onError={() => setImageError(true)}
                  resizeMode="cover"
                />
              ) : (
                <View style={[styles.eventImagePlaceholder, { backgroundColor: memoizedTheme.colors.primaryContainer }]}>
                  <MaterialCommunityIcons name="calendar-blank-outline" size={32} color={memoizedTheme.colors.primary} />
                </View>
              )}
              <View style={styles.eventInfo}>
                <Text style={[styles.eventTitle, {color: theme.system.text}]} numberOfLines={1}>
                  {event.title}
                </Text>
                <View style={styles.eventDetailsItem}>
                  <MaterialCommunityIcons name="calendar" size={16} color={theme.system.secondaryText} />
                  <Text style={[styles.eventDetailsText, {color: theme.system.secondaryText}]}>
                    {formatEventDateTime(event.start_time, i18n.language)}
                  </Text>
                </View>
                <View style={styles.eventDetailsItem}>
                  <MaterialCommunityIcons name="map-marker" size={16} color={theme.system.secondaryText} />
                  <Text style={[styles.eventDetailsText, {color: theme.system.secondaryText}]} numberOfLines={1}>
                    {displayLocation}
                  </Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </Card>
      </View>
    );
  };

  const PostsCard = ({ postsItem }: { postsItem: PostListPayload }) => {
    const [imageError, setImageError] = React.useState(false);
    const bannerMediaItem = postsItem.media_items?.find(item => item.is_banner) || postsItem.media_items?.[0];
    const rawImageUrl = bannerMediaItem?.file_path;
    const imageUrl = getValidImageUrl(rawImageUrl);

    return (
      <View style={styles.newsCardContainer}>
        <Card style={[styles.newsCard, { backgroundColor: theme.system.background }]} mode="elevated">
          <TouchableOpacity
            onPress={() => router.push({ pathname: '/explore/posts/PostsDetailScreen', params: { postsId: postsItem.id, postsOrgId: postsItem.organization_id } })}
            style={styles.newsCardTouchable}
            activeOpacity={0.7}
          >
            {imageUrl && !imageError ? (
              <Image
                source={{ uri: imageUrl }}
                style={styles.newsCardImage}
                onError={() => setImageError(true)}
                resizeMode="cover"
              />
            ) : (
              <View style={[styles.newsCardImage, { backgroundColor: theme.colors.primaryContainer, alignItems: 'center', justifyContent: 'center' }]}>
                <MaterialCommunityIcons name="image-off" size={48} color={theme.colors.primary} />
              </View>
            )}
            <View style={styles.newsCardContent}>
              <Text
                style={[styles.newsCardTitle, { color: theme.system.text }]}
                numberOfLines={2}
              >
                {postsItem.title}
              </Text>
              <View style={styles.newsCardFooter}>
                <View style={styles.newsCardAuthor}>
                  <MaterialCommunityIcons
                    name="account"
                    size={14}
                    color={theme.system.secondaryText}
                    style={styles.newsCardIcon}
                  />
                  <Text style={[styles.newsCardMeta, { color: theme.system.secondaryText }]}>
                    {postsItem.author_display_name || t('common.noAuthor')}
                  </Text>
                </View>
                <View style={styles.newsCardDate}>
                  <MaterialCommunityIcons
                    name="clock-outline"
                    size={14}
                    color={theme.system.secondaryText}
                    style={styles.newsCardIcon}
                  />
                  <Text style={[styles.newsCardMeta, { color: theme.system.secondaryText }]}>
                    {formatPostDate(postsItem.updated_at, i18n.language)}
                  </Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </Card>
      </View>
    );
  };

  const handleHeaderOrgPress = () => {
    if (availableOrganizations && availableOrganizations.length > 1) {
      setShowOrgModal(true);
    }
  };

  const renderOrgSwitchModal = () => {
    return (
    <Modal
      visible={showOrgModal}
      transparent
      animationType="none"
      onRequestClose={() => handleOrgSwitchClose()}
    >
      <TouchableWithoutFeedback onPress={() => handleOrgSwitchClose()}>
        <Animated.View style={[styles.orgSwitchOverlay, { opacity: orgSwitchAnimation }]}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.orgSwitchContent,
                { transform: [{ translateY: orgSwitchTranslateY }] }
              ]}
            >
              <View style={styles.orgSwitchHandle} />
              <Text style={styles.orgSwitchTitle}>{t('dashboard.selectOrganization')}</Text>

              {isLoadingOrganizations ? (
                <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', minHeight: 150, maxHeight: Dimensions.get('window').height * 0.5, paddingVertical: 20 }}>
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                </View>
              ) : organizationsError ? (
                <View style={styles.emptyStateContainer}>
                  <MaterialCommunityIcons name="alert-circle-outline" size={48} color={theme.system.error} />
                  <Text style={styles.errorTitle}>{t('error.title')}</Text>
                  <Text style={styles.errorText}>{t('error.message', { context: 'organizations' })}</Text>
                </View>
              ) : (
                <>
                  <ScrollView style={styles.orgSwitchOptionsContainer} contentContainerStyle={styles.orgSwitchOptionsContent}>
                    {availableOrganizations?.map((org) => {
                      const itemSpecificTheme = createTheme(org.theme_color || 'red');
                      return (
                        <TouchableOpacity
                          key={org.id}
                          style={[
                            styles.orgSwitchOption,
                            selectedOrgIdForSwitch === org.id && {
                              backgroundColor: itemSpecificTheme.colors.primaryContainer
                            }
                          ]}
                          onPress={() => setSelectedOrgIdForSwitch(org.id || '')}
                        >
                          <View style={styles.orgSwitchOrgContent}>
                            <Image
                              source={typeof org.image_url === 'string' ? { uri: org.image_url } : org.image_url || default_organization_logo}
                              style={styles.orgSwitchOrgLogo}
                              resizeMode="contain"
                            />
                            <Text style={[
                              styles.orgSwitchOptionText,
                               {color: selectedOrgIdForSwitch === org.id ? itemSpecificTheme.colors.primary : theme.system.text},
                              selectedOrgIdForSwitch === org.id && styles.orgSwitchSelectedText,
                            ]}>
                              {org.name}
                            </Text>
                          </View>
                          {selectedOrgIdForSwitch === org.id && (
                            <MaterialCommunityIcons
                              name="check"
                              size={24}
                              color={itemSpecificTheme.colors.primary}
                            />
                          )}
                        </TouchableOpacity>
                      );
                    }) || []}
                  </ScrollView>
                  <TouchableOpacity
                    style={[styles.orgSwitchConfirmButton, { backgroundColor: theme.colors.primary }]}
                    onPress={handleOrgSwitchConfirm}
                  >
                    <Text style={styles.orgSwitchConfirmButtonText}>{t('dashboard.switchOrganization')}</Text>
                  </TouchableOpacity>
                </>
              )}
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </Modal>
  )};

  if (!currentOrganization && isLoadingOrganizations) {
    return (
        <View style={[styles.container, {justifyContent: 'center', alignItems: 'center'}]}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text>Loading organizations...</Text>
        </View>
    );
  }

  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={handleHeaderOrgPress}
        activeOpacity={0.8}
        disabled={!availableOrganizations || availableOrganizations.length <= 1}
      >
        <View style={styles.welcomeHeader}>
          <View style={styles.headerContent}>
            <View style={styles.logoContainer}>
              <Image
                source={typeof currentOrganization?.image_url === 'string' ? { uri: currentOrganization.image_url } : currentOrganization?.image_url || default_organization_logo}
                style={styles.logo}
                resizeMode="contain"
              />
              {availableOrganizations && availableOrganizations.length > 1 && (
                <MaterialCommunityIcons
                  name="chevron-down"
                  size={20}
                  color={theme.system.secondaryText}
                  style={styles.logoChevron}
                />
              )}
            </View>
            <View style={styles.welcomeText}>
              <Text style={styles.greeting}>
                {currentOrganization?.name || t('dashboard.welcome.greeting')}
              </Text>
              <Text style={styles.welcomeSubtitle}>
                {currentOrganization?.description || t('dashboard.welcome.subtitle')}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>

      <ScrollView 
        style={{ flex: 1 }} 
        contentContainerStyle={{ paddingBottom: 16 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]} // for Android
            tintColor={theme.colors.primary} // for iOS
          />
        }
      >
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {t('dashboard.commonFeatures.title')}
            </Text>
          </View>
          <View style={styles.featuresGrid}>
            <FeatureButton
              icon="magnify"
              label={t('dashboard.commonFeatures.searchEvents')}
              onPress={handleSearchPress}
            />
            <FeatureButton
              icon="chart-box-outline"
              label={t('dashboard.commonFeatures.myStats')}
              onPress={() => router.push('/user-profile/StatsScreen')}
              requiresAuth
            />
            <FeatureButton
              icon="history"
              label={t('dashboard.commonFeatures.eventHistory')}
              onPress={() => router.push({ pathname: '/tabs/myEvents', params: { dateFilter: 'past', typeFilter: 'all' } })}
              requiresAuth
            />
            <FeatureButton
              icon="help-circle-outline"
              label={t('dashboard.commonFeatures.helpCenter')}
              onPress={() => router.push('/documents/HelpCenterScreen')}
            />
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('dashboard.posts.title')}</Text>
            <TouchableOpacity onPress={handleViewAllPosts}>
              <Text style={styles.viewAllButton}>{t('common.viewAll')}</Text>
            </TouchableOpacity>
          </View>
          {isFetchingPosts ? (
            <View style={styles.loadingContainer}><ActivityIndicator size="large" color={theme.colors.primary} /></View>
          ) : postsError ? (
            <View style={styles.emptyStateContainer}>
              <MaterialCommunityIcons name="alert-circle-outline" size={48} color={theme.system.error} />
              <Text style={styles.errorTitle}>{t('error.title')}</Text>
              <Text style={styles.errorText}>{t('error.message', { context: 'posts' })}</Text>
            </View>
          ) : posts.length > 0 ? (
            <View style={{ marginHorizontal: -16 }}>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                directionalLockEnabled={Platform.OS !== 'web'}
                nestedScrollEnabled={true}
                contentContainerStyle={{ paddingHorizontal: 16, paddingVertical: 4 }}
                overScrollMode="never"
              >
                {posts.map((item) => (
                  <PostsCard key={item.id} postsItem={item} />
                ))}
              </ScrollView>
            </View>
          ) : (
            <View style={styles.emptyStateContainer}>
              <MaterialCommunityIcons name="newspaper-variant-multiple-outline" size={48} color={theme.system.border} />
              <Text style={styles.noContentText}>{t('dashboard.posts.empty')}</Text>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('dashboard.upcomingEvents.title')}</Text>
            <TouchableOpacity onPress={handleViewAllEvents}>
              <Text style={styles.viewAllButton}>{t('common.viewAll')}</Text>
            </TouchableOpacity>
          </View>
          {isFetchingEvents ? (
            <View style={styles.loadingContainer}><ActivityIndicator size="large" color={theme.colors.primary} /></View>
          ) : eventsError ? (
            <View style={styles.emptyStateContainer}>
              <MaterialCommunityIcons name="alert-circle-outline" size={48} color={theme.system.error} />
              <Text style={styles.errorTitle}>{t('error.title')}</Text>
              <Text style={styles.errorText}>{t('error.message', { context: 'events' })}</Text>
            </View>
          ) : upcomingEvents.length > 0 ? (
            <View style={styles.eventsList}>
              {upcomingEvents.map((event) => (
                <EventCard key={event.id} event={event} />
              ))}
            </View>
          ) : (
            <View style={styles.emptyStateContainer}>
              <MaterialCommunityIcons name="calendar-blank" size={48} color={theme.system.border} />
              <Text style={styles.noContentText}>{t('my_events.noUpcomingEvents')}</Text>
            </View>
          )}
        </View>
      </ScrollView>

      {renderOrgSwitchModal()}

      <AuthModal
        visible={showAuthModal}
        onClose={() => {
          setShowAuthModal(false);
          setPendingAction(null);
        }}
        onLogin={handleLogin}
      />
    </View>
  );
}