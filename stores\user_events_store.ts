import { create } from 'zustand';
import { AxiosError } from 'axios';
import type {
    EventRegistrationPayload,
} from '@/api/api_config';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface RegisteredEventsListState {
    registeredEventsList: EventRegistrationPayload[];
    isFetching: boolean;
    error: AxiosError | null;
    setRegisteredEventsList: (participants: EventRegistrationPayload[]) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const registeredEventsListStore = create<RegisteredEventsListState>()(persist<RegisteredEventsListState>(
    (set) => ({
        registeredEventsList: [],
        isFetching: false,
        error: null,
        setRegisteredEventsList: (participants: EventRegistrationPayload[]) => set({ registeredEventsList: participants, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'registered-events-list-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));

// State for Registered Event Details
interface RegisteredEventDetailsState {
    registeredEventDetails: EventRegistrationPayload | null;
    isFetching: boolean;
    error: AxiosError | null;
    setRegisteredEventDetails: (participant: EventRegistrationPayload) => void;
    setError: (error: AxiosError | null) => void;
    setIsFetching: (isFetching: boolean) => void;
}

export const registeredEventDetailsStore = create<RegisteredEventDetailsState>()(persist<RegisteredEventDetailsState>(
    (set) => ({
        registeredEventDetails: null,
        isFetching: false,
        error: null,
        setRegisteredEventDetails: (participant: EventRegistrationPayload) => set({ registeredEventDetails: participant, isFetching: false, error: null }),
        setError: (error: AxiosError | null) => set({ error: error, isFetching: false }),
        setIsFetching: (isFetching: boolean) => set({ isFetching: isFetching }),
    }),
    {
        name: 'registered-event-details-storage',
        storage: createJSONStorage(() => AsyncStorage),
    }
));
