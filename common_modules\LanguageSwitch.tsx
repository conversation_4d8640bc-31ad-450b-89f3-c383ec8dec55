import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
  Platform,
  Modal,
  TouchableWithoutFeedback,
} from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import { userProfileStore } from 'stores/user_store';
import { authenticationStore } from 'stores/authentication_store';
import { useUpdateUserProfile } from '@/api/user_services';

interface Props {
  visible: boolean;
  onClose: () => void;
}

export const LanguageSwitch: React.FC<Props> = ({
  visible,
  onClose,
}) => {
  const { t, i18n } = useTranslation();
  const theme = appStyleStore(state => state.theme);
  const profile = userProfileStore(state => state.profile);
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);
  const { mutateAsync: updateProfileMutation } = useUpdateUserProfile();
  const [selectedLang, setSelectedLang] = React.useState<string>(i18n.language);
  const animationRef = React.useRef(new Animated.Value(0));
  const [isConfirming, setIsConfirming] = React.useState(false);
  const confirmingRef = React.useRef(false);
  const langToApplyRef = React.useRef<string | null>(null);

  const options = [
    { label: '繁體中文', value: 'zh-HK' },
    { label: 'English', value: 'en' },
  ];

  // Initialize the selected language when the component mounts
  React.useEffect(() => {
    setSelectedLang(i18n.language);
  }, [i18n.language]);

  // Handle modal visibility and animation
  React.useEffect(() => {
    if (visible) {
      // Reset state when modal opens
      confirmingRef.current = false;
      langToApplyRef.current = null;
      setIsConfirming(false);
      
      // Reset animation value to 0 and start animation
      animationRef.current.setValue(0);
      Animated.timing(animationRef.current, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true,
      }).start();
    } else {
      // Animate closing
      Animated.timing(animationRef.current, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  // Handle language changes after modal closes
  React.useEffect(() => {
    if (!visible && langToApplyRef.current) {
      const langToSet = langToApplyRef.current;
      
      // Apply language change after animation completes
      const timer = setTimeout(async () => {
        if (langToSet && langToSet !== i18n.language) {
          i18n.changeLanguage(langToSet);

          if (isAuthenticated && langToSet !== profile?.interface_language) {
            try {
              await updateProfileMutation({
                language_preferences: {
                  communication_language: langToSet,
                  interface_language: langToSet,
                },
              });
              // userProfileStore will be updated by the onSuccess callback in useUpdateUserProfile
            } catch (error) {
              console.error('[LanguageSwitch] Error updating language settings on backend:', error);
            }
          }
        }
        langToApplyRef.current = null;
      }, 250);
      
      return () => clearTimeout(timer);
    }
  }, [visible, i18n, isAuthenticated, profile, updateProfileMutation]);

  const translateY = animationRef.current.interpolate({
    inputRange: [0, 1],
    outputRange: [Dimensions.get('window').height, 0],
  });

  const handleClose = () => {
    if (confirmingRef.current) return;
    langToApplyRef.current = null;
    onClose();
  };

  const handleConfirm = () => {
    if (confirmingRef.current) return;
    confirmingRef.current = true;
    setIsConfirming(true);

    langToApplyRef.current = selectedLang;
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={handleClose}
    >
      <TouchableWithoutFeedback onPress={handleClose}>
        <Animated.View style={[styles.overlay, { opacity: animationRef.current }]}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.content,
                {
                  transform: [{ translateY }],
                  backgroundColor: theme.colors.surface,
                }
              ]}
            >
              <View style={styles.handle} />
              <Text style={[styles.title, { color: theme.system.text }]}>{t('profile.items.language.choose')}</Text>
              <View style={styles.optionsContainer}>
                {options.map((option) => (
                  <TouchableOpacity 
                    key={option.value}
                    style={[
                      styles.option,
                      selectedLang === option.value && [
                        styles.selectedOption,
                        { backgroundColor: theme.colors.primaryContainer }
                      ]
                    ]}
                    onPress={() => setSelectedLang(option.value)}
                  >
                    <Text style={[
                      styles.optionText,
                      { color: theme.system.text },
                      selectedLang === option.value && [
                        styles.selectedText,
                        { color: theme.colors.primary }
                      ]
                    ]}>
                      {option.label}
                    </Text>
                    {selectedLang === option.value && (
                      <MaterialCommunityIcons name="check" size={24} color={theme.colors.primary} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
              <TouchableOpacity
                style={[styles.confirmButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleConfirm}
                disabled={isConfirming}
              >
                <Text style={styles.confirmButtonText}>{t('common.confirm')}</Text>
              </TouchableOpacity>
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  content: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    paddingTop: 12,
    paddingBottom: Platform.OS === 'ios' ? 34 : 24,
  },
  handle: {
    width: 36,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    marginLeft: 12,
  },
  optionsContainer: {
    marginBottom: 24,
    paddingHorizontal: 12,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 56,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  selectedOption: {
  },
  optionText: {
    fontSize: 16,
    flex: 1,
  },
  selectedText: {
    fontWeight: '600',
  },
  confirmButton: {
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 12,
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
}); 