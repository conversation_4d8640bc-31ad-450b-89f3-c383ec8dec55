import i18next, { type i18n as I18n } from 'i18next'; // Import base i18next and I18n type
import { initReactI18next } from 'react-i18next';

// Import translations
import en from './locales/en';
import zhHK from './locales/zh-HK';

// Create a specific instance of i18next
const instance: I18n = i18next.createInstance();

export const initializeI18n = async (): Promise<I18n> => {
  // Prevent re-initialization if called multiple times or already initialized
  if (instance.isInitialized) {
    return instance;
  }
  
  await instance
    .use(initReactI18next)
    .init({
      debug: true,
      lng: 'zh-HK', // Default to Traditional Chinese for better UX
      fallbackLng: 'en', // Fallback language
      resources: {
        en: { translation: en },
        'zh-HK': { translation: zhHK },
      },
      interpolation: {
        escapeValue: false, // React already protects from XSS
      },
      react: {
        useSuspense: false, // Assuming you are not using Suspense for translations
      },
      // detection: undefined, // Explicitly undefined or remove, default is no detection
    });

  // console.log('[i18n] Instance initialization complete. Details:', {
  //   language: instance.language,
  //   languages: instance.languages, // Corrected property name
  //   isInitialized: instance.isInitialized,
  //   optionsLng: instance.options.lng,
  //   optionsFallbackLng: instance.options.fallbackLng,
  //   // optionsDetection: JSON.stringify(instance.options.detection), // might be verbose
  // });
  return instance;
};

// Function to change language
export const changeLanguage = async (language: string) => {
  console.trace("[i18n] changeLanguage trace"); // Keep trace for debugging
  if (instance.isInitialized) {
    await instance.changeLanguage(language);
  } else {
    console.warn('[i18n] Attempted to change language on an uninitialized instance.');
  }
};

// Export the created and configured instance
export default instance; 