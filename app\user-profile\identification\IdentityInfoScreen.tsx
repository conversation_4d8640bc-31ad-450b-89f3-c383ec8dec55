import React, { useState, useCallback, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, TextInput, Alert, Platform, KeyboardAvoidingView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { TFunction } from 'i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { appStyleStore } from 'stores/app_style_store';
import { authenticationStore } from 'stores/authentication_store';
import { userProfileStore } from 'stores/user_store';
import { Checkbox, RadioButton } from 'react-native-paper';
import { CountryPicker, Country } from '@/common_modules/CountryPicker';
import { VerificationTypeEnum } from 'types/enums';
import { createTheme } from 'theme/index';

export interface FormData {
  documentType?: VerificationTypeEnum;
  hkidChineseName?: string;
  hkidEnglishName?: string;
  hkidNumber?: string;
  hkidDob?: string;
  hkidGender?: 'M' | 'F';
  hkidPermanentResident?: boolean;
  hkidChineseCommercialCode?: string;
  mainlandIdChineseName?: string;
  mainlandIdNumber?: string;
  mainlandIdDob?: string;
  mainlandIdGender?: 'M' | 'F';
  mainlandIdExpiryDate?: string;
  mainlandTravelPermitNumber?: string;
  mainlandTravelPermitIssueDate?: string;
  mainlandTravelPermitExpiryDate?: string;
  passportNumber?: string;
  passportIssuingCountry?: string;
  passportIssueDate?: string;
  passportExpiryDate?: string;
  hkYouthMembershipNumber?: string;
  addressUnit?: string;
  addressFloor?: string;
  addressBuilding?: string;
  addressStreet?: string;
  addressDistrict?: string;
  addressRegion?: 'NT' | 'KLN' | 'HKI';
  studentIdSchoolName?: string;
  studentIdGrade?: string;
  studentIdExpiryDate?: string;
  softCopy?: boolean;
  applicantName?: string;
  applicantPhone?: string;
  terms?: string;
}

export type FormErrors = {
  [K in keyof FormData]?: string;
};

export type TouchedFields = {
  [K in keyof FormData]?: boolean;
};

export const initialFormData: FormData = {
  documentType: undefined,
  hkidChineseName: '',
  hkidEnglishName: '',
  hkidNumber: '',
  hkidDob: '',
  hkidGender: undefined,
  hkidPermanentResident: undefined,
  hkidChineseCommercialCode: '',
  mainlandIdChineseName: '',
  mainlandIdNumber: '',
  mainlandIdDob: '',
  mainlandIdGender: undefined,
  mainlandIdExpiryDate: '',
  mainlandTravelPermitNumber: '',
  mainlandTravelPermitIssueDate: '',
  mainlandTravelPermitExpiryDate: '',
  passportNumber: '',
  passportIssuingCountry: '',
  passportIssueDate: '',
  passportExpiryDate: '',
  hkYouthMembershipNumber: '',
  addressUnit: '',
  addressFloor: '',
  addressBuilding: '',
  addressStreet: '',
  addressDistrict: '',
  addressRegion: undefined,
  studentIdSchoolName: '',
  studentIdGrade: '',
  studentIdExpiryDate: '',
  applicantName: '',
  applicantPhone: '',
};

interface FieldOption {
  labelKey: string;
  value: string | boolean | 'M' | 'F' | 'NT' | 'KLN' | 'HKI';
}

export interface FieldConfig {
  labelKey: string;
  placeholderKey?: string;
  fieldType: 'text' | 'date' | 'gender' | 'booleanRadio' | 'booleanCheckbox' | 'country' | 'number' | 'regionPicker';
  required?: boolean;
  keyboardType?: 'default' | 'numeric' | 'email-address' | 'phone-pad' | 'number-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  maxLength?: number;
  options?: FieldOption[];
  validationRegex?: RegExp;
  customValidation?: (value: any, t: TFunction, formData?: FormData) => string;
  formatter?: (value: string) => string;
}

// New Proposed Regex for HKID
const HKID_NUMBER_REGEX = /^(?:(?:[A-HJ-NP-RT-WY-Z])|(?:EC|WX|X[A-H]))[0-9]{6}\([0-9A]\)$/i;
const DATE_DDMMYYYY_REGEX = /^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[012])-(19\d\d|20\d\d)$/;
const FOUR_DIGIT_CODE_REGEX = /^\d{4}$/;

// Add Regex for Mainland ID
const MAINLAND_ID_NUMBER_REGEX = /^(\d{17}[0-9X])$/i; // 18 chars, last can be X (case insensitive for input)

// Add Regex for Mainland Travel Permit Number (example, adjust if specific format is known)
// Common format is M + 8 digits, or C + 8 digits. Some older ones might differ.
// This regex is a general idea: Letter + 8 to 11 digits, or just a series of digits/letters.
const MAINLAND_TRAVEL_PERMIT_REGEX = /^[A-Z0-9]{8,12}$/i; // Example: ********* or ********* or just numbers/letters.

// The old formatHkidNumber function is no longer needed for direct input formatting
// as the new two-input system handles this. It might be removed or simplified
// if no other part of the system relies on its specific behavior for already formatted strings.
// For now, let's comment it out or remove if it's confirmed to be unused.
/*
const formatHkidNumber = (value: string): string => {
  let formatted = value.replace(/[^A-Za-z0-9()]/g, '').toUpperCase();
  if (formatted.length > 0 && !/^[A-Z]/.test(formatted)) {
    formatted = 'A' + formatted;
  }
  const match = formatted.match(/^([A-Z]{1,2})(\d{6})([0-9A])?$/);
  if (match && match[3] && !formatted.endsWith(')')) {
    formatted = `${match[1]}${match[2]}(${match[3]})`;
  }
  return formatted.slice(0, 11);
};
*/

const formatDateInput = (text: string): string => {
  const numbers = text.replace(/\D/g, '');
  if (numbers.length <= 2) return numbers;
  if (numbers.length <= 4) return `${numbers.slice(0, 2)}-${numbers.slice(2)}`;
  return `${numbers.slice(0, 2)}-${numbers.slice(2, 4)}-${numbers.slice(4, 8)}`;
};

// HKID Checksum Validation Function
const isValidHkidChecksum = (hkidString: string): boolean => {
  const hkid = hkidString.toUpperCase(); // Assumes caller has stripped parentheses
  // hkidString is like P123456A or XA1234567 or P123456 (if digit not yet entered)

  if (hkid.length < 7 || hkid.length > 8) return false;

  const hasProvidedCheckDigit = hkid.length === 8;
  const providedCheckDigit = hasProvidedCheckDigit ? hkid.substring(7, 8) : '';
  // coreHkidWithoutCheckDigit will be the first 7 characters, e.g., P123456 or XA12345 (if prefix is XA)
  const coreHkidWithoutCheckDigit = hkid.substring(0, 7);

  let prefixStr: string;
  let digitsStr: string;

  // Determine prefix and its length from coreHkidWithoutCheckDigit
  // Check for specific two-letter prefixes first, then single letter.
  const firstTwoChars = coreHkidWithoutCheckDigit.substring(0, 2);
  const firstChar = coreHkidWithoutCheckDigit.substring(0, 1);

  if (/^(EC|WX|XA|XB|XC|XD|XE|XF|XG|XH)$/.test(firstTwoChars)) { // More explicit two-letter check
    prefixStr = firstTwoChars;
    digitsStr = coreHkidWithoutCheckDigit.substring(2);
  } else if (/^[A-HJ-NP-RT-WY-Z]$/.test(firstChar)) { // Valid single letters (excluding I,O,Q,U,X not covered above)
    prefixStr = firstChar;
    digitsStr = coreHkidWithoutCheckDigit.substring(1);
  } else {
    return false; // Not a valid HKID prefix pattern
  }

  if (digitsStr.length !== 6 || !/^[0-9]{6}$/.test(digitsStr)) {
    return false; // Digits part must be 6 numeric characters
  }
  
  if (hasProvidedCheckDigit && !/^[0-9A]$/.test(providedCheckDigit)) {
    return false; // Provided check digit must be a number or 'A'
  }

  const charValues: { [key: string]: number } = {
    ' ': 36, 'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16, 'H': 17, 'I': 18, 'J': 19,
    'K': 20, 'L': 21, 'M': 22, 'N': 23, 'O': 24, 'P': 25, 'Q': 26, 'R': 27, 'S': 28, 'T': 29, 'U': 30,
    'V': 31, 'W': 32, 'X': 33, 'Y': 34, 'Z': 35,
  };
  for (let i = 0; i <= 9; i++) {
    charValues[i.toString()] = i;
  }

  let c1Val, c2Val;
  if (prefixStr.length === 1) {
    c1Val = charValues[' ']; // Space for single-letter prefixes
    c2Val = charValues[prefixStr[0]]; // Already uppercase from hkid.toUpperCase()
  } else { // length is 2
    c1Val = charValues[prefixStr[0]];
    c2Val = charValues[prefixStr[1]];
  }

  if (c1Val === undefined || c2Val === undefined) {
      // This case should ideally not be reached if prefixStr and digitsStr are correctly parsed
      return false; 
  }

  let sum = (c1Val * 9) + (c2Val * 8);
  for (let i = 0; i < 6; i++) {
    const digitVal = charValues[digitsStr[i]];
    if (digitVal === undefined) return false; // Should not happen if digitsStr is validated
    sum += digitVal * (7 - i);
  }

  const remainder = sum % 11;
  let expectedCheckDigitStr: string;
  if (remainder === 0) {
    expectedCheckDigitStr = '0';
  } else if (remainder === 1) {
    expectedCheckDigitStr = 'A';
  } else {
    expectedCheckDigitStr = (11 - remainder).toString();
  }
  
  // If no check digit was provided (hkid.length === 7), the check implicitly passes for now.
  // If it was provided (hkid.length === 8), it must match the calculated one.
  return hasProvidedCheckDigit ? expectedCheckDigitStr === providedCheckDigit : true;
};

export const validateField = (fieldName: keyof FormData, value: any, config: FieldConfig, t: TFunction, formData?: FormData): string => {
  const translatedFieldName = t(config.labelKey);
  if (config.required && (value === undefined || value === '' || value === null)) {
    if (config.fieldType === 'booleanRadio' && value === false) {
      // It's a valid selection, not "empty"
    } else if (config.fieldType === 'country' && !value) {
      return t('identity.info.validation.countryRequired');
    } else if (config.fieldType === 'regionPicker' && !value) {
      return t('identity.info.validation.regionRequired');
    } else {
      return t('identity.info.validation.genericRequired', { fieldName: translatedFieldName });
    }
  }

  if (value === undefined || value === '' || value === null) return '';

  if (config.fieldType === 'booleanCheckbox' && config.required && value === false) {
      return t('validation.mustBeChecked', { fieldName: translatedFieldName });
  }

  if (config.fieldType === 'date') {
    if (!DATE_DDMMYYYY_REGEX.test(value as string)) {
      return t('identity.info.validation.dateOfBirthInvalid');
    }
    const [day, month, year] = (value as string).split('-').map(Number);
    const dateObj = new Date(year, month - 1, day);
    if (dateObj.getFullYear() !== year || dateObj.getMonth() !== month - 1 || dateObj.getDate() !== day) {
      return t('identity.info.validation.dateOfBirthInvalid');
    }

    const isExpiryField = fieldName.toLowerCase().includes('expirydate');
    const isIssueField = fieldName.toLowerCase().includes('issuedate');
    const isDobField = fieldName.toLowerCase().includes('dob');

    if (isDobField && dateObj > new Date()) {
      return t('identity.info.validation.dateOfBirthFuture');
    }
    if (isExpiryField && dateObj < new Date()) {
      return t('identity.info.validation.expiryDatePast');
    }
    if (isIssueField && dateObj > new Date()) {
      return t('identity.info.validation.issueDateFuture');
    }
  }

  if (config.validationRegex && !config.validationRegex.test(value as string)) {
    if (fieldName === 'hkidNumber') return t('identity.info.validation.idCardNumberInvalid');
    if (fieldName === 'mainlandIdNumber') return t('identity.info.validation.mainlandIdInvalid');
    if (fieldName === 'mainlandTravelPermitNumber') return t('identity.info.validation.mainlandTravelPermitInvalid');
    if (fieldName === 'passportNumber') return t('identity.info.validation.passportInvalid');
    return t('validation.invalidFormat', { fieldName: translatedFieldName });
  }
  
  if (fieldName === 'hkidNumber' && !HKID_NUMBER_REGEX.test(value as string)){
      return t('identity.info.validation.idCardNumberInvalid');
  }
  if (fieldName === 'mainlandIdNumber' && !MAINLAND_ID_NUMBER_REGEX.test(value as string)){
      return t('identity.info.validation.mainlandIdInvalid');
  }
  if (fieldName === 'mainlandTravelPermitNumber' && value && MAINLAND_TRAVEL_PERMIT_REGEX && !MAINLAND_TRAVEL_PERMIT_REGEX.test(value as string)){
      return t('identity.info.validation.mainlandTravelPermitInvalid');
  }

  if (config.customValidation) {
    return config.customValidation(value, t, formData);
  }
  return '';
};

const getFieldConfigurations = (docType: VerificationTypeEnum | undefined, t: TFunction, formTypeContext?: VerificationTypeEnum): Partial<Record<keyof FormData, FieldConfig>> => {
  if (docType === VerificationTypeEnum.HkIDCard) {
    return {
      hkidChineseName: { labelKey: 'identity.info.hkidChineseName', placeholderKey: 'identity.info.hkidChineseNamePlaceholder', fieldType: 'text', required: false },
      hkidChineseCommercialCode: { 
        labelKey: 'identity.info.hkidChineseCommercialCode', 
        placeholderKey: 'identity.info.hkidChineseCommercialCodePlaceholder', 
        fieldType: 'text',
        required: false, 
        keyboardType: 'default',
        maxLength: 20,
      }, 
      hkidEnglishName: { labelKey: 'identity.info.hkidEnglishName', placeholderKey: 'identity.info.hkidEnglishNamePlaceholder', fieldType: 'text', required: true, autoCapitalize: 'characters' },
      hkidGender: { labelKey: 'identity.info.hkidGender', fieldType: 'gender', required: true, options: [{ labelKey: 'identity.info.male', value: 'M' }, { labelKey: 'identity.info.female', value: 'F' }] },
      hkidDob: { labelKey: 'identity.info.hkidDob', placeholderKey: 'identity.info.dobPlaceholder', fieldType: 'date', required: true, keyboardType: 'number-pad', maxLength: 10, formatter: formatDateInput },
      hkidNumber: { 
        labelKey: 'identity.info.hkidNumber', 
        fieldType: 'text',
        required: true, 
        autoCapitalize: 'characters', 
        maxLength: 11,
        customValidation: (value, t) => {
          if (!HKID_NUMBER_REGEX.test(value)) {
            return t('identity.info.validation.idCardNumberInvalid');
          }
          const coreHkid = value.replace(/[()]/g, '');
          if (!isValidHkidChecksum(coreHkid)) {
            return t('identity.info.validation.idCardNumberInvalidChecksum');
          }
          return '';
        }
      },
      hkidPermanentResident: { labelKey: 'identity.info.hkidPermanentResident', fieldType: 'booleanRadio', required: true, options: [{ labelKey: 'common.yes', value: true }, { labelKey: 'common.no', value: false }] },
    };
  } else if (docType === VerificationTypeEnum.MainlandChinaIDCard) {
    return {
      mainlandIdChineseName: { labelKey: 'identity.info.mainlandIdChineseName', placeholderKey: 'identity.info.mainlandIdChineseNamePlaceholder', fieldType: 'text', required: true },
      mainlandIdGender: { labelKey: 'identity.info.mainlandIdGender', fieldType: 'gender', required: true, options: [{ labelKey: 'identity.info.male', value: 'M' }, { labelKey: 'identity.info.female', value: 'F' }] },
      mainlandIdDob: { labelKey: 'identity.info.mainlandIdDob', placeholderKey: 'identity.info.dobPlaceholder', fieldType: 'date', required: true, keyboardType: 'number-pad', maxLength: 10, formatter: formatDateInput },
      mainlandIdNumber: { labelKey: 'identity.info.mainlandIdNumber', placeholderKey: 'identity.info.mainlandIdNumberPlaceholder', fieldType: 'text', required: true, autoCapitalize: 'characters', maxLength: 18 },
      mainlandIdExpiryDate: { labelKey: 'identity.info.mainlandIdExpiryDate', placeholderKey: 'identity.info.expiryDatePlaceholder', fieldType: 'date', required: true, keyboardType: 'number-pad', maxLength: 10, formatter: formatDateInput },
    };
  } else if (docType === VerificationTypeEnum.MainlandTravelPermit) {
    return {
      mainlandTravelPermitNumber: { labelKey: 'identity.info.mainlandTravelPermitNumber', placeholderKey: 'identity.info.mainlandTravelPermitNumberPlaceholder', fieldType: 'text', required: true, autoCapitalize: 'characters', maxLength: 12 },
      mainlandTravelPermitIssueDate: { labelKey: 'identity.info.mainlandTravelPermitIssueDate', placeholderKey: 'identity.info.issueDatePlaceholder', fieldType: 'date', required: true, keyboardType: 'number-pad', maxLength: 10, formatter: formatDateInput },
      mainlandTravelPermitExpiryDate: { labelKey: 'identity.info.mainlandTravelPermitExpiryDate', placeholderKey: 'identity.info.expiryDatePlaceholder', fieldType: 'date', required: true, keyboardType: 'number-pad', maxLength: 10, formatter: formatDateInput },
    };
  } else if (docType === VerificationTypeEnum.Passport) {
    return {
      passportNumber: { labelKey: 'identity.info.passportNumber', placeholderKey: 'identity.info.passportNumberPlaceholder', fieldType: 'text', required: true, autoCapitalize: 'characters', maxLength: 30 },
      passportIssuingCountry: { labelKey: 'identity.info.passportIssuingCountry', fieldType: 'country', required: true },
      passportIssueDate: { labelKey: 'identity.info.passportIssueDate', placeholderKey: 'identity.info.issueDatePlaceholder', fieldType: 'date', required: true, keyboardType: 'number-pad', maxLength: 10, formatter: formatDateInput },
      passportExpiryDate: { labelKey: 'identity.info.passportExpiryDate', placeholderKey: 'identity.info.expiryDatePlaceholder', fieldType: 'date', required: true, keyboardType: 'number-pad', maxLength: 10, formatter: formatDateInput },
    };
  } else if (docType === VerificationTypeEnum.HkYouthPlus) {
    return {
      hkYouthMembershipNumber: { labelKey: 'identity.info.hkYouthMembershipNumber', placeholderKey: 'identity.info.hkYouthMembershipNumberPlaceholder', fieldType: 'text', required: true, autoCapitalize: 'characters', maxLength: 20 },
    };
  } else if (docType === VerificationTypeEnum.AddressProof) {
    return {
      addressUnit: { labelKey: 'identity.info.addressUnit', placeholderKey: 'identity.info.addressUnitPlaceholder', fieldType: 'text', required: true, maxLength: 50 },
      addressFloor: { labelKey: 'identity.info.addressFloor', placeholderKey: 'identity.info.addressFloorPlaceholder', fieldType: 'text', required: true, maxLength: 50 },
      addressBuilding: { labelKey: 'identity.info.addressBuilding', placeholderKey: 'identity.info.addressBuildingPlaceholder', fieldType: 'text', required: true, maxLength: 100 },
      addressStreet: { labelKey: 'identity.info.addressStreet', placeholderKey: 'identity.info.addressStreetPlaceholder', fieldType: 'text', required: true, maxLength: 100 },
      addressDistrict: { labelKey: 'identity.info.addressDistrict', placeholderKey: 'identity.info.addressDistrictPlaceholder', fieldType: 'text', required: true, maxLength: 50 },
      addressRegion: { labelKey: 'identity.info.addressRegion', fieldType: 'regionPicker', required: true, options: [{ labelKey: 'identity.info.regionHongKongIsland', value: 'HKI' }, { labelKey: 'identity.info.regionKowloon', value: 'KLN' }, { labelKey: 'identity.info.regionNewTerritories', value: 'NT' }] },
    };
  } else if (docType === VerificationTypeEnum.StudentID) {
    return {
      studentIdSchoolName: { labelKey: 'identity.info.studentIdSchoolName', placeholderKey: 'identity.info.studentIdSchoolNamePlaceholder', fieldType: 'text', required: true, maxLength: 100 },
      studentIdGrade: { labelKey: 'identity.info.studentIdGrade', placeholderKey: 'identity.info.studentIdGradePlaceholder', fieldType: 'text', required: true, maxLength: 50 },
      studentIdExpiryDate: { labelKey: 'identity.info.studentIdExpiryDate', placeholderKey: 'identity.info.expiryDatePlaceholder', fieldType: 'date', required: true, keyboardType: 'number-pad', maxLength: 10, formatter: formatDateInput },
    };
  }
  return {};
};

export default function IdentityInfoScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme || createTheme('red'));
  const router = useRouter();
  const params = useLocalSearchParams<{ documentType?: string; showTerms?: string; prefill?: string }>();
  
  // Get user data from stores
  const isAuthenticated = authenticationStore(state => state.isAuthenticated);
  const userProfile = userProfileStore(state => state.profile );

  // State for the two input fields
  const [hkidPrefixInput, setHkidPrefixInput] = useState('');
  const [hkidCheckDigitInput, setHkidCheckDigitInput] = useState('');

  // Refs for focusing inputs
  const hkidPrefixRef = useRef<TextInput>(null);
  const hkidCheckDigitRef = useRef<TextInput>(null);

  const [documentType, setDocumentType] = useState<VerificationTypeEnum | undefined>(undefined);
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<TouchedFields>({});
  const [showTerms, setShowTerms] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);

  const docTypeParam = params.documentType as VerificationTypeEnum | undefined;
  const showTermsParam = params.showTerms === 'true';
  const prefillParam = params.prefill === 'true';

  const userNameFromAuth = userProfile?.display_name;
  const userPhoneFromAuth = userProfile?.phone;

  useEffect(() => {
    setDocumentType(docTypeParam);

    if (docTypeParam) {
      let newFormData: FormData = {
        ...initialFormData,
        documentType: docTypeParam as VerificationTypeEnum,
      };

      if (docTypeParam === VerificationTypeEnum.HomeVisit) {
        newFormData.applicantName = userNameFromAuth || '';
        newFormData.applicantPhone = userPhoneFromAuth || '';
      }
      setFormData(newFormData);
    } else {
      Alert.alert(
        t('common.error'),
        t('identity.errors.noDocType'),
        [{ text: t('common.ok'), onPress: () => router.canGoBack() ? router.back() : router.replace('/tabs/profile') }]
      );
    }

    setShowTerms(showTermsParam);
    setAgreedToTerms(false);
    setErrors({});
    setTouched({});
    setSelectedCountry(null);

  }, [docTypeParam, showTermsParam, userNameFromAuth, userPhoneFromAuth, t, router]);

  // Effect to parse formData.hkidNumber to populate individual input states
  useEffect(() => {
    const fullHkid = formData.hkidNumber || '';
    // Regex to match "ABCDEFG(H)" or "ABCDEFG" or "ABCDEFG()"
    const hkidPattern = /^([A-Z0-9]{0,7})(?:\(([A-Z0-9]?)\))?$/i;
    const match = fullHkid.match(hkidPattern);

    if (match) {
      setHkidPrefixInput((match[1] || '').toUpperCase());
      setHkidCheckDigitInput((match[2] || '').toUpperCase());
    } else {
      // Fallback for unexpected formats or partial inputs not yet in parens form
      const prefixCandidate = fullHkid.replace(/[^A-Z0-9]/gi, '').substring(0, 7).toUpperCase();
      const digitCandidate = fullHkid.replace(/[^A-Z0-9]/gi, '').substring(7, 8).toUpperCase();
      setHkidPrefixInput(prefixCandidate);
      if (fullHkid.length > 7 && !fullHkid.includes('(')) { // e.g. "P123456A" from an old record
          setHkidCheckDigitInput(digitCandidate);
      } else {
          setHkidCheckDigitInput(''); // Default to empty if no clear digit part
      }
    }
  }, [formData.hkidNumber]); // Runs when formData.hkidNumber changes externally or on init

  const fieldConfigs = getFieldConfigurations(documentType, t, documentType);
  
  const currentValidateField = useCallback((fieldName: keyof FormData, value: any, config: FieldConfig): string => {
    return validateField(fieldName, value, config, t, formData);
  }, [t, formData]);

  const validateCurrentForm = (): boolean => {
    if (!documentType) return false;
    let isValid = true;
    const currentErrors: FormErrors = {};
    const currentTouched: TouchedFields = {};

    for (const fieldName in fieldConfigs) {
        const key = fieldName as keyof FormData;
        const config = fieldConfigs[key];
        if (config) {
            const error = currentValidateField(key, formData[key], config);
            if (error) {
                currentErrors[key] = error;
                isValid = false;
            }
            currentTouched[key] = true;
        }
    }
    setErrors(currentErrors);
    setTouched(currentTouched);

    if (showTerms && !agreedToTerms) {
      isValid = false;
      currentErrors.terms = t('identity.validation.termsRequired');
      currentTouched.terms = true;
    }
    
    return isValid;
  };

  const handleNext = () => {
    if (!documentType) return;

    if (!validateCurrentForm()) {
        if (showTerms && !agreedToTerms && !errors.terms) {
             Alert.alert(t('common.error'), t('identity.validation.termsRequired'));
        }
      return;
    }

    if (documentType === VerificationTypeEnum.HomeVisit) {
      console.warn(`IdentityInfoScreen reached for ${documentType}. This type should be handled by its dedicated screen.`);
      Alert.alert(t('common.error'), `Unexpected document type: ${documentType}`);
      return;
    } 

    router.push({
      pathname: '/user-profile/identification/IdentityFrontScreen',
      params: {
        formData: JSON.stringify(formData),
        documentType: documentType
      },
    });
  };
  
  const handleChange = (name: keyof FormData, value: string | boolean | undefined | 'M' | 'F' | 'NT' | 'KLN' | 'HKI') => {
    let processedValue = value;
    const config = fieldConfigs[name];
    if (config?.formatter && typeof value === 'string') {
      processedValue = config.formatter(value);
    }

    // Convert specific fields to uppercase 
    if (typeof value === 'string' && (
      name === 'hkidEnglishName' ||
      name === 'hkidNumber' ||
      name === 'mainlandIdNumber' ||
      name === 'mainlandTravelPermitNumber' ||
      name === 'passportNumber' ||
      name === 'hkYouthMembershipNumber'
    )) {
      processedValue = value.toUpperCase();
    }

    setFormData(prev => ({ ...prev, [name]: processedValue }));
    
    if (touched[name] && config) {
      const error = currentValidateField(name, processedValue, config);
      setErrors(prev => ({ ...prev, [name]: error || undefined }));
    }
  };

  const handleBlur = (name: keyof FormData) => {
    setTouched(prev => ({ ...prev, [name]: true } as TouchedFields));
    const config = fieldConfigs[name];
    if (config) {
      const error = currentValidateField(name, formData[name], config);
      setErrors(prev => ({ ...prev, [name]: error || undefined }));
    }
  };
  
  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    const fieldName = 'passportIssuingCountry' as keyof FormData;
    const newValue = country.code;

    setFormData(prevFormData => {
      const newFormData = { ...prevFormData, [fieldName]: newValue };
      
      // Validate with the new value and the context of the updated form data.
      // The fieldConfigs should be available in this scope.
      const config = fieldConfigs[fieldName];
      if (config) {
        const error = validateField(fieldName, newValue, config, t, newFormData);
        setErrors(prevErrors => ({ ...prevErrors, [fieldName]: error || undefined }));
      }
      
      return newFormData; // Return the new state for formData
    });

    // Mark the field as touched after the selection and validation attempt.
    setTouched(prevTouched => ({ ...prevTouched, [fieldName]: true }));
  };
  
  const updateFormHkidNumber = (prefix: string, digit: string) => {
    let combinedHkid = prefix;
    if (digit) { // If digit is present, always include it with whatever prefix exists.
      combinedHkid = `${prefix}(${digit || ''})`;
    } else if (prefix.length === 7) { // Only add parentheses if prefix is full and digit is empty
      combinedHkid = `${prefix}()`;
    }
    // Call the main handleChange for the form's hkidNumber field
    handleChange('hkidNumber', combinedHkid);
  };

  const handleHkidPrefixChange = (text: string) => {
    const sanitizedText = text.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
    
    if (sanitizedText.length <= 7) {
      setHkidPrefixInput(sanitizedText);
      updateFormHkidNumber(sanitizedText, hkidCheckDigitInput);
    } else if (sanitizedText.length === 8) { // User typed/pasted 8th character
      const newPrefix = sanitizedText.substring(0, 7);
      const newCheckDigit = sanitizedText.substring(7, 8);
      setHkidPrefixInput(newPrefix);
      setHkidCheckDigitInput(newCheckDigit);
      updateFormHkidNumber(newPrefix, newCheckDigit);
      hkidCheckDigitRef.current?.focus(); // Focus the check digit input
    }
    // MaxLength property on TextInput should prevent >8, but good to be safe.
  };
  
  const handleHkidCheckDigitChange = (text: string) => {
    const sanitizedText = text.replace(/[^A-Za-z0-9]/g, '').toUpperCase().substring(0, 1);
    setHkidCheckDigitInput(sanitizedText);
    updateFormHkidNumber(hkidPrefixInput, sanitizedText);
  };

  const componentStyles = StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.system.background },
    scrollView: { flex: 1 },
    scrollContent: { flexGrow: 1, paddingBottom: 24 },
    form: { paddingHorizontal: 24, paddingTop: 16 },
    headerContainer: { 
        paddingHorizontal: 16, 
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: theme.system.border,
    },
    headerTitle: { 
        fontSize: 22, 
        fontWeight: 'bold', 
        color: theme.system.text 
    },
    formGroup: {
        marginBottom: 20,
    },
    label: {
        fontSize: 15,
        fontWeight: '600',
        color: theme.system.text,
        marginBottom: 8,
    },
    input: {
        height: 48,
        borderWidth: 1,
        borderColor: theme.system.border,
        borderRadius: 8,
        paddingHorizontal: 16,
        fontSize: 16,
        color: theme.system.text,
        backgroundColor: theme.system.background,
    },
    inputError: {
        borderColor: theme.system.error,
    },
    errorText: {
        fontSize: 13,
        color: theme.system.error,
        marginTop: 4,
        minHeight: 16,
    },
    footer: {
      paddingHorizontal: 24,
      paddingVertical: 16,
      borderTopWidth: 1,
      borderTopColor: theme.system.border,
      backgroundColor: theme.system.background,
    },
    nextButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      paddingVertical: 16,
      borderRadius: 12,
      gap: 8,
    },
    nextButtonDisabled: {
      backgroundColor: theme.colors.primaryDisabled || '#CCCCCC',
    },
    nextButtonText: {
      fontSize: 17,
      fontWeight: '600',
      color: '#FFFFFF',
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 8,
    },
    checkboxLabel: {
        fontSize: 15,
        marginLeft: 8,
        color: theme.system.text,
        flexShrink: 1,
    },
    radioButtonGroupContainer: {
      flexDirection: 'column',
    },
    radioButtonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: Platform.OS === 'ios' ? 0 : 8,
    },
    radioButtonLabel: {
        fontSize: 15,
        marginLeft: 0,
        color: theme.system.text,
        flexShrink: 1,
    },
    termsContainer: {
      marginTop: 20,
      padding: 15,
      backgroundColor: theme.system.background,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.system.border,
    },
    termsTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.system.text,
      marginBottom: 8,
    },
    termsContent: {
      fontSize: 14,
      color: theme.system.secondaryText,
      lineHeight: 20,
      marginBottom: 10,
    },
    dateInputTouchable: { // Style for the tappable date field
      height: 48,
      borderWidth: 1,
      borderColor: theme.system.border,
      borderRadius: 8,
      paddingHorizontal: 16,
      justifyContent: 'center',
      backgroundColor: theme.system.background,
    },
    dateInputText: {
      fontSize: 16,
      color: theme.system.text,
    },
    dateInputPlaceholder: {
      fontSize: 16,
      color: theme.system.secondaryText,
    },
  });

  const renderFormField = (fieldName: keyof FormData, config: FieldConfig) => {
    const isDocumentField = (
      fieldName === 'hkidEnglishName' ||
      fieldName === 'mainlandIdNumber' ||
      fieldName === 'mainlandTravelPermitNumber' ||
      fieldName === 'passportNumber' ||
      fieldName === 'hkYouthMembershipNumber'
    );

    const commonInputProps = {
      placeholder: config.placeholderKey ? t(config.placeholderKey) : undefined,
      onBlur: () => handleBlur(fieldName),
      style: [componentStyles.input, errors[fieldName] ? componentStyles.inputError : {}],
      placeholderTextColor: theme.system.secondaryText,
      keyboardType: config.keyboardType || 'default',
      autoCapitalize: (fieldName === 'hkidEnglishName' || fieldName === 'mainlandIdNumber' || fieldName === 'mainlandTravelPermitNumber' || fieldName === 'passportNumber' || fieldName === 'hkYouthMembershipNumber') ? 'characters' : (config.autoCapitalize || 'none'),
      maxLength: config.maxLength,
    };

    if (fieldName === 'hkidNumber') {
      return (
        <View key={fieldName} style={componentStyles.formGroup}>
          <Text style={componentStyles.label}>
            {t(config.labelKey)}
            {config.required && <Text style={{ color: theme.system.error }}> *</Text>}
          </Text>
          <View style={[{
            flexDirection: 'row',
            alignItems: 'center',
            height: componentStyles.input.height, // from base input style
            borderRadius: componentStyles.input.borderRadius, // from base input style
            borderWidth: 1,
            borderColor: errors[fieldName] ? theme.system.error : theme.system.border, // Error state on container
            backgroundColor: theme.system.background, // Background on container
          }]}>
            <TextInput
              ref={hkidPrefixRef}
              value={hkidPrefixInput}
              onChangeText={handleHkidPrefixChange}
              maxLength={8}
              placeholder={t('identity.info.hkidPrefixPlaceholder', 'A111111')} // Updated fallback
              style={{
                flex: 3,
                height: '100%',
                paddingHorizontal: componentStyles.input.paddingHorizontal, // Use standard padding for this part
                fontSize: componentStyles.input.fontSize,
                color: theme.system.text,
                // No border, no individual background
              }}
              keyboardType="default"
              autoCapitalize="characters"
              onBlur={() => handleBlur(fieldName)}
              placeholderTextColor={theme.system.secondaryText}
            />
            <Text style={{
              paddingHorizontal: 5, // Reduced padding
              fontSize: 16,
              color: theme.system.text,
              lineHeight: componentStyles.input.height, // Vertically center
              // No border, no individual background
            }}>{'('}</Text>
            <TextInput
              ref={hkidCheckDigitRef}
              value={hkidCheckDigitInput}
              onChangeText={handleHkidCheckDigitChange}
              maxLength={1}
              placeholder={t('identity.info.hkidCheckDigitPlaceholder', '9')} // Fallback matches user's edit
              style={{
                flex: 1,
                height: '100%',
                textAlign: 'center',
                paddingHorizontal: 0, // No horizontal padding for single centered char
                fontSize: componentStyles.input.fontSize,
                color: theme.system.text,
                // No border, no individual background
              }}
              keyboardType="default"
              autoCapitalize="characters"
              onBlur={() => handleBlur(fieldName)}
              placeholderTextColor={theme.system.secondaryText}
            />
            <Text style={{
              paddingHorizontal: 5, // Reduced padding
              fontSize: 16,
              color: theme.system.text,
              lineHeight: componentStyles.input.height, // Vertically center
              // No border, no individual background
            }}>{')'}</Text>
          </View>
          {errors[fieldName] && <Text style={componentStyles.errorText}>{errors[fieldName]}</Text>}
        </View>
      );
    }

    return (
      <View key={fieldName} style={componentStyles.formGroup}>
        <Text style={componentStyles.label}>
          {t(config.labelKey)}
          {config.required && <Text style={{ color: theme.system.error }}> *</Text>}
        </Text>
        {config.fieldType === 'text' && <TextInput {...commonInputProps} value={String(formData[fieldName] ?? '')} onChangeText={(text) => handleChange(fieldName, text)} />}
        {config.fieldType === 'number' && <TextInput {...commonInputProps} value={String(formData[fieldName] ?? '')} onChangeText={(text) => handleChange(fieldName, text)} keyboardType="number-pad" />}
        {config.fieldType === 'date' && (
          <TextInput 
            {...commonInputProps} 
            value={String(formData[fieldName] ?? '')}
            onChangeText={(text) => handleChange(fieldName, text)}
            keyboardType="number-pad"
          />
        )}
        {config.fieldType === 'gender' && config.options && (
          <RadioButton.Group
            onValueChange={newValue => handleChange(fieldName, newValue as 'M' | 'F')}
            value={(formData[fieldName] as 'M' | 'F' | undefined) ?? ''}
          >
            <View style={{ flexDirection: 'row', justifyContent: 'flex-start', marginTop: 8 }}>
              {config.options.map(opt => (
                <View key={opt.value as string} style={[componentStyles.radioButtonContainer, { marginRight: 20 }]}>
                  <RadioButton.Android value={opt.value as string} color={theme.colors.primary} />
                  <Text style={componentStyles.radioButtonLabel} onPress={() => handleChange(fieldName, opt.value as 'M' | 'F')}>{t(opt.labelKey)}</Text>
                </View>
              ))}
            </View>
          </RadioButton.Group>
        )}
        {config.fieldType === 'booleanRadio' && config.options && (
           <RadioButton.Group 
            onValueChange={newValue => handleChange(fieldName, newValue === 'true')} 
            value={String(formData[fieldName])}
          >
            <View style={{ flexDirection: 'row', justifyContent: 'flex-start', marginTop: 8 }}>
              {config.options.map(opt => (
                <View key={String(opt.value)} style={[componentStyles.radioButtonContainer, { marginRight: 20 }]}>
                  <RadioButton.Android value={String(opt.value)} color={theme.colors.primary} />
                  <Text style={componentStyles.radioButtonLabel} onPress={() => handleChange(fieldName, opt.value as boolean)}>{t(opt.labelKey)}</Text>
                </View>
              ))}
            </View>
          </RadioButton.Group>
        )}
        {config.fieldType === 'booleanCheckbox' && (
          <TouchableOpacity 
            style={[componentStyles.checkboxContainer, { justifyContent: 'flex-start'}]}
            onPress={() => handleChange(fieldName, !formData[fieldName])}
            activeOpacity={0.7}
          >
            <Checkbox.Android 
                status={!!formData[fieldName] ? 'checked' : 'unchecked'}
                onPress={() => handleChange(fieldName, !formData[fieldName])} 
                color={theme.colors.primary}
            />
          </TouchableOpacity>
        )}
        {config.fieldType === 'country' && fieldName === 'passportIssuingCountry' && (
           <CountryPicker
            selectedCountry={selectedCountry}
            onSelectCountry={handleCountrySelect}
            countryCode={formData.passportIssuingCountry}
            error={errors.passportIssuingCountry}
            touched={touched.passportIssuingCountry}
          />
        )}
        {config.fieldType === 'regionPicker' && config.options && (
          <RadioButton.Group
            onValueChange={newValue => handleChange(fieldName, newValue as 'NT' | 'KLN' | 'HKI')}
            value={(formData[fieldName] as 'NT' | 'KLN' | 'HKI' | undefined) ?? ''}
          >
            <View style={componentStyles.radioButtonGroupContainer}>
              {config.options.map(opt => (
                <View key={opt.value as string} style={componentStyles.radioButtonContainer}>
                  <RadioButton.Android value={opt.value as string} color={theme.colors.primary} />
                  <Text style={componentStyles.radioButtonLabel} onPress={() => handleChange(fieldName, opt.value as 'NT' | 'KLN' | 'HKI')}>{t(opt.labelKey)}</Text>
                </View>
              ))}
            </View>
          </RadioButton.Group>
        )}
        {touched[fieldName] && errors[fieldName] && <Text style={componentStyles.errorText}>{errors[fieldName]}</Text>}
        {(!touched[fieldName] || !errors[fieldName]) && <Text style={componentStyles.errorText}> </Text>}
      </View>
    );
  };

  const renderFormFields = () => {
    if (!documentType) return <Text style={{color: theme.system.text, padding: 20}}>{t('common.loading')}</Text>;
    const fieldConfigs = getFieldConfigurations(documentType, t, documentType);
    if (Object.keys(fieldConfigs).length === 0 && (documentType !== VerificationTypeEnum.HomeVisit)) {
        return <Text style={{color: theme.system.text, padding: 20}}>{t('identity.errors.noFieldsConfigured', {docType: documentType})}</Text>;
    }
    return Object.keys(fieldConfigs).map(fieldName => {
      const key = fieldName as keyof FormData;
      const config = fieldConfigs[key];
      if (!config) return null;
      return renderFormField(key, config);
    });
  };
  
  const renderTermsAndConditions = () => {
    if (!showTerms || !documentType) return null;

    const termsTitleKey = `identity.terms.${documentType}.title`;
    const termsContentKey = `identity.terms.${documentType}.content`;
    const defaultTermsTitleKey = 'identity.terms.default.title';
    const defaultTermsContentKey = 'identity.terms.default.content';

    return (
      <View style={componentStyles.termsContainer}>
        <Text style={componentStyles.termsTitle}>
            {t([termsTitleKey, defaultTermsTitleKey])}
        </Text>
        <Text style={componentStyles.termsContent}>
            {t([termsContentKey, defaultTermsContentKey])}
        </Text>
        <TouchableOpacity 
            style={componentStyles.checkboxContainer}
            onPress={() => setAgreedToTerms(!agreedToTerms)}
            activeOpacity={0.7}
        >
          <Checkbox.Android
            status={agreedToTerms ? 'checked' : 'unchecked'}
            onPress={() => setAgreedToTerms(!agreedToTerms)}
            color={theme.colors.primary}
          />
          <Text style={componentStyles.checkboxLabel}>
            {t('identity.terms.agreeToTerms')}
          </Text>
        </TouchableOpacity>
        {touched.terms && errors.terms && <Text style={componentStyles.errorText}>{errors.terms}</Text>}
      </View>
    );
  };

  const isSubmitDisabled = (showTerms && !agreedToTerms);

  return (
    <>
      <Stack.Screen options={{
        headerTitle: t('identity.info.title'),
      }} />
      <SafeAreaView style={componentStyles.container} edges={['bottom']}>
        <StatusBar style={Platform.OS === 'ios' ? 'dark' : 'light'} backgroundColor={theme.system.background} />
        <KeyboardAvoidingView 
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{ flex: 1 }}
          keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
        >
          <View style={componentStyles.headerContainer}>
            <Text style={componentStyles.headerTitle}>{t('identity.info.defaultTitle')}</Text>
          </View>
          <ScrollView 
            style={componentStyles.scrollView} 
            contentContainerStyle={componentStyles.scrollContent}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            automaticallyAdjustKeyboardInsets={true}
          >
            <View style={componentStyles.form}>
              {renderFormFields()}
              {showTerms && renderTermsAndConditions()}
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
        
        <View style={componentStyles.footer}>
          <TouchableOpacity 
            style={[
              componentStyles.nextButton, 
              isSubmitDisabled && componentStyles.nextButtonDisabled
            ]} 
            onPress={handleNext}
            disabled={isSubmitDisabled}
            activeOpacity={isSubmitDisabled ? 1 : 0.7}
          >
            <Text style={componentStyles.nextButtonText}>
              {isSubmitDisabled ? t('common.loading') : 
                (documentType === VerificationTypeEnum.HomeVisit ? t('common.submit') : t('identity.info.next'))}
            </Text>
            {(!isSubmitDisabled && documentType !== VerificationTypeEnum.HomeVisit) && 
              <MaterialCommunityIcons name="arrow-right" size={20} color="#FFFFFF" />}
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </>
  );
}