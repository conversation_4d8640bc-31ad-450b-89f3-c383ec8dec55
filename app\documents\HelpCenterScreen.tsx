import React, { useState, useEffect, useCallback } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  SafeAreaView,
  TouchableOpacity,
  TouchableWithoutFeedback,
  TextInput,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Stack } from 'expo-router';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { appStyleStore } from 'stores/app_style_store';

// Custom animated accordion item component to prevent hook rules violations
const AccordionItem = ({ 
  item, 
  isExpanded, 
  onPress, 
  renderAnswer,
  theme
}: { 
  item: any, 
  isExpanded: boolean, 
  onPress: () => void, 
  renderAnswer: (answer: string) => React.ReactNode,
  theme: any
}) => {
  // Animation for rotating the arrow icon
  const rotateValue = useSharedValue(0);
  
  // Animation for expanding/collapsing the answer container
  const heightValue = useSharedValue(0);
  
  useEffect(() => {
    rotateValue.value = withTiming(isExpanded ? 1 : 0, { duration: 200 });
    heightValue.value = withTiming(isExpanded ? 1 : 0, { duration: 200 });
  }, [isExpanded, rotateValue, heightValue]);
  
  const rotateStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${rotateValue.value * 180}deg` }],
    };
  });
  
  const heightStyle = useAnimatedStyle(() => {
    return {
      opacity: heightValue.value,
      maxHeight: heightValue.value * 1000,
      overflow: 'hidden',
    };
  });

  const styles = getThemedStyles(theme);

  return (
    <TouchableWithoutFeedback onPress={onPress}>
      <View style={styles.faqItem}>
        <View style={styles.faqHeader}>
          <Text style={styles.question}>{item.question}</Text>
          <Animated.View style={rotateStyle}>
            <MaterialCommunityIcons
              name="chevron-down"
              size={20}
              color={theme.system.secondaryText}
            />
          </Animated.View>
        </View>
        <Animated.View style={[styles.answerContainer, heightStyle]}>
          {renderAnswer(item.answer)}
        </Animated.View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export function HelpCenterScreen() {
  const { t } = useTranslation();
  const theme = appStyleStore(state => state.theme);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedIds, setExpandedIds] = useState<Set<string>>(new Set());

  const styles = getThemedStyles(theme);

  // Get all FAQ items
  const faqItems = t('helpCenter.faq.items', { returnObjects: true }) as Record<string, { question: string, answer: string }>;
  
  // Create FAQ data with correct category mapping
  const faqData = Object.entries(faqItems).map(([key, item]) => {
    const categoryKey = key.split('Phone')[0]?.split('Code')[0]?.split('Event')[0]?.split('Account')[0] || '';
    let category = '';
    
    if (categoryKey.includes('change') || categoryKey.includes('sms') || categoryKey.includes('account')) {
      category = 'accountSecurity';
    } else if (categoryKey.includes('identity') || categoryKey.includes('verification') || categoryKey.includes('volunteer')) {
      category = 'identityVerification';
    } else if (categoryKey.includes('qr')) {
      category = 'qrCode';
    } else if (categoryKey.includes('registration')) {
      category = 'eventRegistration';
    } else if (categoryKey.includes('check') || categoryKey.includes('cancel') || categoryKey.includes('add')) {
      category = 'eventManagement';
    } else if (categoryKey.includes('notification')) {
      category = 'notifications';
    }

    return {
      id: key,
      category: t(`helpCenter.faq.categories.${category}`),
      question: item.question,
      answer: item.answer,
    };
  });

  const filteredFAQs = faqData.filter(faq => 
    (faq.question || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
    (faq.answer || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
    (faq.category || '').toLowerCase().includes(searchQuery.toLowerCase())
  );

  const groupedFAQs = filteredFAQs.reduce((acc, faq) => {
    if (!acc[faq.category]) {
      acc[faq.category] = [];
    }
    acc[faq.category].push(faq);
    return acc;
  }, {} as Record<string, typeof faqData>);

  const handleFAQPress = useCallback((id: string) => {
    setExpandedIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  }, []);

  const renderAnswer = useCallback((answer: string) => {
    return (answer || '').split('\n').map((line, index) => {
      if (line.match(/^\d+\./)) {
        return (
          <View key={index} style={styles.stepContainer}>
            <Text style={styles.stepText}>{line}</Text>
          </View>
        );
      } else if (line.trim() === '') {
        return <View key={index} style={styles.spacer} />;
      } else {
        return <Text key={index} style={styles.regularText}>{line}</Text>;
      }
    });
  }, [styles]);

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{
          headerShown: true,
          headerTitle: t('profile.items.helpCenter.title'),
        }} 
      />
      <View style={styles.searchContainer}>
        <MaterialCommunityIcons name="magnify" size={20} color={theme.system.secondaryText} />
        <TextInput
          style={styles.searchInput}
          placeholder={t('helpCenter.searchPlaceholder')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor={theme.system.secondaryText}
        />
        {searchQuery !== '' && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <MaterialCommunityIcons name="close" size={20} color={theme.system.secondaryText} />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {Object.entries(groupedFAQs).map(([category, items], index) => (
          <View key={category} style={[
            styles.categorySection,
            index === 0 && styles.firstCategorySection
          ]}>
            <Text style={styles.categoryTitle}>{category}</Text>
            {items.map(item => (
              <AccordionItem
                key={item.id}
                item={item}
                isExpanded={expandedIds.has(item.id)}
                onPress={() => handleFAQPress(item.id)}
                renderAnswer={renderAnswer}
                theme={theme}
              />
            ))}
          </View>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const getThemedStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: theme.system.border,
    marginHorizontal: 16,
    marginVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    lineHeight: 20,
    color: theme.system.text,
    height: 40,
    padding: 0,
    paddingVertical: 0,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  categorySection: {
    marginBottom: 24,
  },
  firstCategorySection: {
    marginTop: 12,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.system.secondaryText,
    marginBottom: 12,
  },
  faqItem: {
    backgroundColor: theme.system.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: theme.system.border,
  },
  faqHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  question: {
    fontSize: 15,
    fontWeight: '500',
    color: theme.system.text,
    flex: 1,
    marginRight: 8,
  },
  answerContainer: {
    marginTop: 0,
  },
  stepContainer: {
    marginVertical: 4,
  },
  stepText: {
    fontSize: 14,
    color: theme.system.secondaryText,
    lineHeight: 20,
  },
  regularText: {
    fontSize: 14,
    color: theme.system.secondaryText,
    lineHeight: 20,
    marginVertical: 4,
  },
  spacer: {
    height: 8,
  },
});

export default HelpCenterScreen;
