import React, { useState, useRef, useCallback } from 'react';
import {
    View,
    Text,
    StyleSheet,
    Image,
    Dimensions,
    TouchableOpacity,
    FlatList,
    ViewToken,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { MediaItemPayload } from '@/api/api_config';
import { getValidImageUrl } from 'utils/imageUtils';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const IMAGE_HEIGHT = SCREEN_HEIGHT * 0.35;

interface EventImageCarouselProps {
    mediaItems: MediaItemPayload[];
    onImagePress: (index: number) => void;
    theme: any;
}

// Check if file is an image based on extension
const isImageFile = (fileName: string | undefined): boolean => {
    if (!fileName) return false;
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'heic', 'heif'];
    return imageTypes.includes(extension);
};

// Get only image items
const getImageItems = (mediaItems: MediaItemPayload[]): MediaItemPayload[] => {
    return mediaItems.filter(item => isImageFile(item.file_name));
};

export const ImageCarousel: React.FC<EventImageCarouselProps> = ({
    mediaItems,
    onImagePress,
    theme,
}) => {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [imageErrors, setImageErrors] = useState<{ [key: string]: boolean }>({});
    const viewabilityConfig = useRef({ itemVisiblePercentThreshold: 50 }).current;

    const imageItems = getImageItems(mediaItems);

    const onViewableItemsChanged = useCallback((info: { viewableItems: Array<ViewToken<MediaItemPayload>> }) => {
        const firstViewable = info.viewableItems[0];
        if (firstViewable && firstViewable.index !== null) {
            setCurrentImageIndex(firstViewable.index);
        }
    }, []);

    const handleImageError = (imageId: string) => {
        setImageErrors(prev => ({ ...prev, [imageId]: true }));
    };

    const renderImageItem = ({ item, index }: { item: MediaItemPayload; index: number }) => {
        const imageUrl = getValidImageUrl(item.file_path);
        const hasError = imageErrors[item.id] || !imageUrl;

        return (
            <TouchableOpacity
                style={styles.imageContainer}
                onPress={() => onImagePress(index)}
                activeOpacity={0.9}
            >
                {!hasError ? (
                    <Image
                        source={{
                            uri: imageUrl,
                            cache: 'reload'
                        }}
                        style={styles.image}
                        resizeMode="cover"
                        onError={() => handleImageError(item.id)}
                    />
                ) : (
                    <View style={[
                        styles.image,
                        styles.imagePlaceholder,
                        { backgroundColor: theme.colors.surfaceDisabled || '#e0e0e0' }
                    ]}>
                        <MaterialCommunityIcons
                            name="image-off"
                            size={48}
                            color={theme.colors.onSurfaceDisabled || '#a0a0a0'}
                        />
                    </View>
                )}
                
            </TouchableOpacity>
        );
    };

    const renderPaginationDots = () => {
        if (imageItems.length <= 1) return null;

        return (
            <View style={styles.paginationBackground}>
                <View style={styles.paginationWrapper}>
                    <View style={styles.paginationContainer}>
                        {imageItems.map((_, index) => (
                            <View
                                key={`dot-${index}`}
                                style={[
                                    styles.paginationDot,
                                    index === currentImageIndex
                                        ? styles.paginationDotActive
                                        : styles.paginationDotInactive
                                ]}
                            />
                        ))}
                    </View>
                </View>
            </View>
        );
    };

    // If no images, show placeholder
    if (imageItems.length === 0) {
        return (
            <View style={[
                styles.imageContainer,
                styles.imagePlaceholder,
                { backgroundColor: theme.colors.surfaceDisabled || '#e0e0e0' }
            ]}>
                <MaterialCommunityIcons
                    name="image-off"
                    size={48}
                    color={theme.colors.onSurfaceDisabled || '#a0a0a0'}
                />
            </View>
        );
    }

    // If only one image, show it directly without pagination
    if (imageItems.length === 1) {
        return renderImageItem({ item: imageItems[0], index: 0 });
    }

    return (
        <View style={styles.container}>
            <FlatList
                data={imageItems}
                renderItem={renderImageItem}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                keyExtractor={(item, index) => `carousel-${item.id}-${index}`}
                onViewableItemsChanged={onViewableItemsChanged}
                viewabilityConfig={viewabilityConfig}
                getItemLayout={(data, index) => (
                    { length: SCREEN_WIDTH, offset: SCREEN_WIDTH * index, index }
                )}
            />
            {renderPaginationDots()}
            

        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        height: IMAGE_HEIGHT,
        width: SCREEN_WIDTH,
        position: 'relative',
    },
    imageContainer: {
        height: IMAGE_HEIGHT,
        width: SCREEN_WIDTH,
    },
    image: {
        width: '100%',
        height: '100%',
    },
    imagePlaceholder: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    paginationBackground: {
        position: 'absolute',
        bottom: 28,
        left: 0,
        right: 0,
        alignItems: 'center',
    },
    paginationWrapper: {
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        borderRadius: 16,
        paddingHorizontal: 6,
        paddingVertical: 5,
    },
    paginationContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
    },
    paginationDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        marginHorizontal: 4,
    },
    paginationDotActive: {
        backgroundColor: '#FFFFFF',
    },
    paginationDotInactive: {
        backgroundColor: 'rgba(255, 255, 255, 0.5)',
    },

}); 

export default ImageCarousel;