import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Text, useWindowDimensions, Platform } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter } from 'expo-router'; // Changed from @react-navigation/native
import { authenticationStore } from '@/stores/authentication_store'; // Import authentication store
import { useAccessTokenRefresh } from '@/api/authentication_services'; // Import token refresh hook

const ANIMATION_DURATION = 600; // 主动画时间缩短到600ms

// 定义图标类型以解决TypeScript错误
type IconName = keyof typeof MaterialCommunityIcons.glyphMap;

export default function SplashScreen() {
  const router = useRouter(); // Changed from useNavigation
  const { width, height } = useWindowDimensions(); // Use hook

  // Calculate dimension-dependent constants here
  const CIRCLE_SIZE = width * 0.7;
  const ICON_SIZE = width * 0.15;

  // Define styles dynamically or pass dimensions
  const styles = getStyles(width, height, CIRCLE_SIZE, ICON_SIZE);

  // 添加缩放和透明度动画值用于过渡效果
  const scaleOut = useRef(new Animated.Value(1)).current;
  const fadeOut = useRef(new Animated.Value(1)).current;
  
  // Main circle animations
  const scale = useRef(new Animated.Value(0)).current;
  const rotate = useRef(new Animated.Value(0)).current;
  
  // Icon animations
  const icon1 = useRef(new Animated.Value(0)).current;
  const icon2 = useRef(new Animated.Value(0)).current;
  const icon3 = useRef(new Animated.Value(0)).current;
  const icon4 = useRef(new Animated.Value(0)).current;
  const icon5 = useRef(new Animated.Value(0)).current;
  
  // Text animations
  const textScale = useRef(new Animated.Value(0.7)).current;
  const textOpacity = useRef(new Animated.Value(0)).current;

  const { mutate: refreshAccessToken, isPending: isRefreshingToken } = useAccessTokenRefresh(); // Use isPending

  const spin = rotate.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg']
  });

  const createIconAnimation = (value: Animated.Value, delay: number) => {
    return Animated.sequence([
      Animated.delay(delay),
      Animated.spring(value, {
        toValue: 1,
        tension: 150, // 增加张力
        friction: 7,  // 减少摩擦
        useNativeDriver: Platform.OS !== 'web' // Conditionally set useNativeDriver
      })
    ]);
  };

  useEffect(() => {
    const attemptAutoLogin = async () => {
      const { refreshToken, accessToken, isAuthenticated } = authenticationStore.getState();
      console.log('[SplashScreen] Initial auth state:', { refreshToken, accessToken, isAuthenticated });

      if (refreshToken) {
        console.log('[SplashScreen] Attempting to refresh token...');
        refreshAccessToken(
          { refresh_token: refreshToken },
          {
            onSuccess: (data) => {
              console.log('[SplashScreen] Token refresh successful:', data);
              // onSuccess in useAccessTokenRefresh already updates the store
              // Proceed with app launch animation and navigation
              startAnimationsAndNavigate('/tabs/dashboard');
            },
            onError: (error) => {
              console.error('[SplashScreen] Token refresh failed:', error);
              // onError in useAccessTokenRefresh handles store update (clearing tokens on 401)
              // Navigate to login
              startAnimationsAndNavigate('/login');
            },
          }
        );
      } else {
        console.log('[SplashScreen] No refresh token found. Navigating to login.');
        // If no refresh token, navigate to login after animation
        startAnimationsAndNavigate('/login');
      }
    };

    const startAnimationsAndNavigate = (targetPath: string) => {
      const mainAnimation = Animated.sequence([
        Animated.parallel([
          Animated.spring(scale, {
            toValue: 1,
            tension: 150,
            friction: 7,
            useNativeDriver: Platform.OS !== 'web'
          }),
          createIconAnimation(icon1, ANIMATION_DURATION * 0.05),
          createIconAnimation(icon2, ANIMATION_DURATION * 0.1),
          createIconAnimation(icon3, ANIMATION_DURATION * 0.15),
          createIconAnimation(icon4, ANIMATION_DURATION * 0.2),
          createIconAnimation(icon5, ANIMATION_DURATION * 0.25),
          Animated.timing(textOpacity, {
            toValue: 1,
            duration: ANIMATION_DURATION * 0.15,
            useNativeDriver: Platform.OS !== 'web'
          }),
          Animated.spring(textScale, {
            toValue: 1,
            tension: 150,
            friction: 7,
            useNativeDriver: Platform.OS !== 'web'
          })
        ]),
        Animated.delay(400),
        Animated.parallel([
          Animated.timing(fadeOut, {
            toValue: 0,
            duration: 150,
            useNativeDriver: Platform.OS !== 'web'
          }),
          Animated.timing(scaleOut, {
            toValue: 1.1,
            duration: 150,
            useNativeDriver: Platform.OS !== 'web'
          })
        ])
      ]);

      Animated.loop(
        Animated.timing(rotate, {
          toValue: 1,
          duration: ANIMATION_DURATION * 1.5,
          useNativeDriver: Platform.OS !== 'web'
        })
      ).start();

      mainAnimation.start(() => {
        router.replace({ pathname: targetPath, params: { fromSplash: "true" } });
      });
    };
    
    // Do not start animations until token refresh logic is complete if applicable
    // The animations will be started by startAnimationsAndNavigate
    if (!isRefreshingToken) {
        attemptAutoLogin();
    }

  }, [isRefreshingToken]); // Add isPending to dependency array

  const renderIcon = (
    Icon: typeof MaterialCommunityIcons,
    name: IconName,
    color: string,
    position: any,
    animatedValue: Animated.Value // Renamed from scale to avoid conflict
  ) => (
    <Animated.View
      style={[
        styles.iconWrapper,
        position,
        {
          transform: [{ scale: animatedValue }] // Use animatedValue
        }
      ]}
    >
      <BlurView intensity={95} style={styles.iconBlur}>
        <LinearGradient
          colors={[color, color.replace('1)', '0.7)')]}
          style={styles.iconGradient}
          start={{ x: 0.3, y: 0 }}
          end={{ x: 0.7, y: 1 }}
        >
          <Icon name={name} size={ICON_SIZE * 0.6} color="#FFFFFF" />
        </LinearGradient>
      </BlurView>
    </Animated.View>
  );

  return (
    <Animated.View style={[styles.container, { 
      opacity: fadeOut,
      transform: [{ scale: scaleOut }]
    }]}>
      {/* Background gradient */}
      <LinearGradient
        colors={['#FFFFFF', '#F8F9FF']}
        style={StyleSheet.absoluteFill}
      />

      {/* Main rotating circle */}
      <Animated.View
        style={[
          styles.mainCircle,
          {
            transform: [
              { scale }, // This 'scale' refers to the main circle's scale animation
              { rotate: spin }
            ]
          }
        ]}
      >
        <BlurView intensity={90} style={styles.circleBlur}>
          <LinearGradient
            colors={['rgba(130, 87, 229, 0.8)', 'rgba(87, 111, 229, 0.6)']}
            style={styles.circleGradient}
            start={{ x: 0.1, y: 0.1 }}
            end={{ x: 0.9, y: 0.9 }}
          />
        </BlurView>
      </Animated.View>

      {/* Community icons */}
      {renderIcon(MaterialCommunityIcons, 'account-group', 'rgba(255, 107, 107, 1)', styles.icon1, icon1)}
      {renderIcon(MaterialCommunityIcons, 'calendar-check', 'rgba(78, 205, 196, 1)', styles.icon2, icon2)}
      {renderIcon(MaterialCommunityIcons, 'chart-box', 'rgba(255, 230, 109, 1)', styles.icon3, icon3)}
      {renderIcon(MaterialCommunityIcons, 'translate', 'rgba(149, 225, 211, 1)', styles.icon4, icon4)}
      {renderIcon(MaterialCommunityIcons, 'card-account-details', 'rgba(255, 184, 108, 1)', styles.icon5, icon5)}

      {/* Text content */}
      <Animated.View
        style={[
          styles.textContainer,
          {
            opacity: textOpacity,
            transform: [{ scale: textScale }]
          }
        ]}
      >
        <Text style={styles.title}>會員系統</Text>
        <Text style={styles.subtitle}>連結社群・成就未來</Text>
      </Animated.View>
    </Animated.View>
  );
}

// Modify getStyles to accept dimensions and calculated sizes
const getStyles = (width: number, height: number, CIRCLE_SIZE: number, ICON_SIZE: number) => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  mainCircle: {
    width: CIRCLE_SIZE,
    height: CIRCLE_SIZE,
    borderRadius: CIRCLE_SIZE / 2,
    overflow: 'hidden',
  },
  circleBlur: {
    width: '100%',
    height: '100%',
    borderRadius: CIRCLE_SIZE / 2,
    overflow: 'hidden',
  },
  circleGradient: {
    width: '100%',
    height: '100%',
    borderRadius: CIRCLE_SIZE / 2,
  },
  iconWrapper: {
    position: 'absolute',
    width: ICON_SIZE,
    height: ICON_SIZE,
    borderRadius: ICON_SIZE / 2,
    overflow: 'hidden',
  },
  iconBlur: {
    width: '100%',
    height: '100%',
    borderRadius: ICON_SIZE / 2,
    overflow: 'hidden',
  },
  iconGradient: {
    width: '100%',
    height: '100%',
    borderRadius: ICON_SIZE / 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon1: {
    top: height * 0.25,
    left: width * 0.2,
  },
  icon2: {
    top: height * 0.35,
    right: width * 0.15,
  },
  icon3: {
    top: height * 0.55,
    left: width * 0.1,
  },
  icon4: {
    top: height * 0.6,
    right: width * 0.25,
  },
  icon5: {
    top: height * 0.75,
    left: width * 0.4,
  },
  textContainer: {
    position: 'absolute',
    bottom: height * 0.15,
    alignItems: 'center',
  },
  title: {
    fontSize: 44,
    fontWeight: '800',
    color: '#2D2D2D',
    marginBottom: 12,
    letterSpacing: 2,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#4A4A4A',
    letterSpacing: 2,
  },
});