import React from 'react';
import { View, Text, StyleSheet, ScrollView, Platform, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button } from 'react-native-paper';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useRouter, Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { StatusBar } from 'expo-status-bar';
import Constants from 'expo-constants'; // Added import for Constants
import { appStyleStore } from 'stores/app_style_store';
import { createTheme } from 'theme/index';

const STATUSBAR_HEIGHT = Platform.OS === 'android' ? Constants.statusBarHeight || 0 : 0;

export default function ChangePhoneGuideScreen() { // Changed to export default
  const { t } = useTranslation();
  const router = useRouter();
  const storedTheme = appStyleStore(state => state.theme);
  const theme = storedTheme || createTheme('red'); // Or your default theme

  const handleContinue = () => {
    router.push('./PhoneVerificationScreen');
  };

  const handleAccountRecovery = () => {
    router.push('../AccountRecoveryScreen');
  };

  return (
    <>
      <Stack.Screen options={{
        headerTitle: t('profile.changePhoneGuide.title'),
      }} />

      <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.system.background }]} edges={['bottom']}>
        <ScrollView
          style={[styles.container, { backgroundColor: theme.system.background }]}
          contentContainerStyle={styles.contentContainer}
        >
          <View style={styles.header}>
            <MaterialCommunityIcons name="cellphone-check" size={54} color={theme.colors.primary} />
            <Text style={[styles.title, { color: theme.system.text }]}>
              {t('profile.changePhoneGuide.title')}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.system.text }]}>
              {t('profile.changePhoneGuide.important')}
            </Text>
            <Text style={[styles.description, { color: theme.system.secondaryText }]}>
              {t('profile.changePhoneGuide.description')}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.system.text }]}>
              {t('profile.changePhoneGuide.howItWorks')}
            </Text>

            <View style={styles.stepContainer}>
              <View style={[styles.stepIcon, { backgroundColor: theme.colors.primaryContainer, opacity: 0.8 }]}>
                <Ionicons name="alert-circle" size={22} color={theme.colors.primary} />
              </View>
              <View style={styles.stepContent}>
                <Text style={[styles.stepTitle, { color: theme.system.text }]}>
                  {t('profile.changePhoneGuide.step1.title')}
                </Text>
                <Text style={[styles.stepDescription, { color: theme.system.secondaryText }]}>
                  {t('profile.changePhoneGuide.step1.description')}
                </Text>
              </View>
            </View>

            <View style={styles.stepContainer}>
              <View style={[styles.stepIcon, { backgroundColor: theme.colors.primaryContainer, opacity: 0.8 }]}>
                <Ionicons name="phone-portrait" size={22} color={theme.colors.primary} />
              </View>
              <View style={styles.stepContent}>
                <Text style={[styles.stepTitle, { color: theme.system.text }]}>
                  {t('profile.changePhoneGuide.step2.title')}
                </Text>
                <Text style={[styles.stepDescription, { color: theme.system.secondaryText }]}>
                  {t('profile.changePhoneGuide.step2.description')}
                </Text>
              </View>
            </View>
          </View>

          <View style={[styles.troubleSection, {
            backgroundColor: 'rgba(255, 152, 0, 0.08)',
            borderColor: 'rgba(245, 124, 0, 0.2)'
          }]}>
            <View style={styles.troubleContent}>
              <Ionicons name="help-circle-outline" size={20} color="#F57C00" style={styles.troubleIcon} />
              <Text style={[styles.troubleTitle, { color: '#F57C00' }]}>
                {t('profile.changePhoneGuide.trouble.title')}
              </Text>
            </View>
            <Text style={[styles.troubleDescription, { color: theme.system.secondaryText }]}>
              {t('profile.changePhoneGuide.trouble.description')}
            </Text>

            {/* <TouchableOpacity
              onPress={handleAccountRecovery}
              style={styles.recoveryLink}
              activeOpacity={0.7}
            >
              <Text style={[styles.recoveryLinkText, { color: '#F57C00' }]}>
                {t('profile.changePhoneGuide.needHelp')}
              </Text>
              <Ionicons name="chevron-forward" size={16} color="#F57C00" />
            </TouchableOpacity> */}
          </View>
        </ScrollView>

        <View style={[styles.footer, { borderTopColor: theme.system.border, backgroundColor: theme.system.background }]}>
          <Button
            mode="contained"
            onPress={handleContinue}
            style={[styles.continueButton, { backgroundColor: theme.colors.primary }]}
            contentStyle={styles.buttonContent}
            labelStyle={styles.buttonLabel}
          >
            {t('profile.changePhoneGuide.continue')}
          </Button>
        </View>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    paddingTop: STATUSBAR_HEIGHT,
  },
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 24,
    paddingBottom: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 22,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 12,
  },
  description: {
    fontSize: 15,
    lineHeight: 22,
    letterSpacing: 0.1,
  },
  stepContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  stepIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  troubleSection: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    borderWidth: 1,
  },
  troubleContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  troubleIcon: {
    marginRight: 8,
  },
  troubleTitle: {
    fontSize: 15,
    fontWeight: '500',
  },
  troubleDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  recoveryLink: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    paddingVertical: 4,
  },
  recoveryLinkText: {
    fontSize: 15,
    fontWeight: '500',
    marginRight: 4,
  },
  footer: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
  },
  continueButton: {
    height: 48,
    justifyContent: 'center',
    borderRadius: 10,
    elevation: 0,
  },
  buttonContent: {
    height: 48,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});
