import { MD3LightTheme } from 'react-native-paper';
import { typography } from './typography';

const themeColors = {
  orange: {
    primary: '#FF8C00',
    secondary: '#FFA500',
    primaryContainer: '#FFF5E6',
    primaryLight: 'rgba(255, 140, 0, 0.3)',
    primaryDisabled: '#FFE5CC',
  },
  green: {
    primary: '#4CAF50',
    secondary: '#66BB6A',
    primaryContainer: '#E8F5E9',
    primaryLight: 'rgba(76, 175, 80, 0.3)',
    primaryDisabled: '#E8F5E9',
  },
  blue: {
    primary: '#2196F3',
    secondary: '#42A5F5',
    primaryContainer: '#E3F2FD',
    primaryLight: 'rgba(33, 150, 243, 0.3)',
    primaryDisabled: '#E3F2FD',
  },
  pink: {
    primary: '#E91E63',
    secondary: '#EC407A',
    primaryContainer: '#FCE4EC',
    primaryLight: 'rgba(233, 30, 99, 0.3)',
    primaryDisabled: '#FCE4EC',
  },
  red: {
    primary: '#F44336',  // Slightly lighter red
    secondary: '#FF6F61', // Slightly lighter pink
    primaryContainer: '#FFEBEE',
    primaryLight: 'rgba(229, 57, 53, 0.3)',
    primaryDisabled: '#FFCDD2',
  },
  // Add WhatsApp theme colors
  whatsapp: {
    primary: '#25D366',      // WhatsApp green
    secondary: '#1DB954',    // WhatsApp dark green for headers
    primaryContainer: '#E8F5E9',
    primaryLight: 'rgba(37, 211, 102, 0.3)',
    primaryDisabled: '#9EE6B6', // Lighter green for disabled states
  }
};

// Static WhatsApp theme that can be imported directly regardless of organization theme
export const whatsAppTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: themeColors.whatsapp.primary,
    primaryContainer: themeColors.whatsapp.primaryContainer,
    secondary: themeColors.whatsapp.secondary,
    primaryLight: themeColors.whatsapp.primaryLight,
    primaryDisabled: themeColors.whatsapp.primaryDisabled,
    error: '#FF3B30',
    background: '#FFFFFF',
  },
  system: {
    participant: '#1890FF',
    volunteer: '#FF69B4',
    text: '#111B21',
    secondaryText: '#667781',
    background: '#FFFFFF',
    border: '#F0F0F0',
    success: '#52C41A',
    error: '#FF3B30',
    info: '#1890FF',
    warning: '#FAAD14',
  },
  typography,
};

export const createTheme = (organizationTheme: string) => {
  const colors = themeColors[organizationTheme as keyof typeof themeColors];
  return {
    ...MD3LightTheme,
    colors: {
      ...MD3LightTheme.colors,
      primary: colors.primary,
      primaryContainer: colors.primaryContainer,
      secondary: colors.secondary,
      primaryLight: colors.primaryLight,
      primaryDisabled: colors.primaryDisabled,
      error: '#FF3B30',
      background: '#FFFFFF',
    },
    system: {
      participant: '#1890FF',
      volunteer: '#FF69B4',
      text: '#333333',
      secondaryText: '#666666',
      background: '#FFFFFF',
      border: '#F0F0F0',
      success: '#52C41A',
      error: '#FF4D4F',
      info: '#1890FF',
      warning: '#FAAD14',
    },
    typography,
  };
};