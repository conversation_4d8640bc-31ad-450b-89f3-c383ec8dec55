import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useRouter, Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import Constants from 'expo-constants';
import { appStyleStore } from 'stores/app_style_store';
import { createTheme } from 'theme/index';
import type { MD3Theme as CustomTheme } from 'react-native-paper';
import { authenticationStore } from 'stores/authentication_store';
import { userProfileStore } from 'stores/user_store';
import {
    useExistingPhoneOtpInitiate
} from '@/api/authentication_services';
import { ExistingPhoneOtpInitiateRequest, PhoneOtpInitiateResponse } from '@/api/api_config';
import { generatePKCE } from '@/utils/pkce';

export default function PhoneVerificationScreen() {
    const router = useRouter();
    const { t } = useTranslation();
    const storedTheme = appStyleStore(state => state.theme);
    const theme = storedTheme || createTheme('red');
    const userProfile = userProfileStore(state => state.profile);
    const { mutateAsync: initiateOtpForOldPhone, error: initiateError } = useExistingPhoneOtpInitiate();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const styles = getStyles(theme);

    const handleProceedWithWhatsApp = async () => {
        try {
            setLoading(true);
            setError(null);

            // Generate PKCE credentials using proper cryptographic functions
            const pkceData = await generatePKCE();

            if (!pkceData || !pkceData.codeVerifier || !pkceData.codeChallenge || !pkceData.state) {
                setError(t('error.pkceGeneration'));
                setLoading(false);
                return;
            }

            // Initiate OTP flow
            const initiateParams: ExistingPhoneOtpInitiateRequest = {
                phone: userProfile?.phone || '',
                phone_otp_channel: 'whatsapp',
                client_id: userProfile?.phone?.replace('+852', '') || '',
                code_challenge: pkceData.codeChallenge,
                code_challenge_method: 'S256',
                state: pkceData.state,
            };
            
            const otpResult: PhoneOtpInitiateResponse = await initiateOtpForOldPhone(initiateParams);

            if (!otpResult || !otpResult.state) {
                setError(t('error.otpInitiation'));
                setLoading(false);
                return;
            }

            // Navigate to code verification screen with the selected method
            router.push({
                pathname: '/user-profile/changePhone/CodeVerificationScreen',
                params: {
                    method: 'whatsapp',
                    phoneNumber: userProfile?.phone,
                    apiState: otpResult.state,
                    codeVerifier: pkceData.codeVerifier
                }
            });

        } catch (err: any) {
            console.error('Phone verification error:', err);
            const errorMessage = initiateError?.message || err.message || t('error.phoneVerification.SYSTEM_ERROR');
            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const displayPhone = userProfile?.phone?.replace('+852', '') || '';

    return (
        <View style={[styles.safeArea, { backgroundColor: theme.system.background }]}>
            <Stack.Screen options={{
                headerTitle: t('profile.changePhone.verifyCurrentPhone'),
                headerTitleStyle: { 
                    color: theme.system.text,
                    fontSize: 18,
                    fontWeight: '600'
                },
                headerShadowVisible: false
            }} />

            <ScrollView
                style={styles.container}
                contentContainerStyle={styles.contentContainer}
            >
                <View style={styles.header}>
                    <MaterialCommunityIcons 
                        name="cellphone-check" 
                        size={60} 
                        color={theme.colors.primary} 
                    />
                    <Text style={[styles.phoneNumber, { color: theme.system.text }]}>
                        {t('auth.phonePrefix')} {displayPhone}
                    </Text>
                </View>

                <View style={styles.section}>
                    <Text style={[styles.description, { color: theme.system.secondaryText }]}>
                        {t('profile.changePhone.verificationInfo')}
                    </Text>
                    
                    {error && (
                        <View style={styles.errorContainer}>
                            <MaterialCommunityIcons name="alert-circle" size={18} color={theme.colors.error} />
                            <Text style={[styles.errorText, { color: theme.colors.error }]}>
                                {error}
                            </Text>
                        </View>
                    )}
                </View>

                <View style={styles.methodsSection}>
                    <Text style={[styles.sectionTitle, { color: theme.system.text }]}>
                        {t('profile.changePhone.selectMethod')}
                    </Text>

                    <TouchableOpacity 
                        style={styles.methodContainer}
                        onPress={handleProceedWithWhatsApp}
                        disabled={loading}
                    >
                        <View style={[styles.methodIcon, { backgroundColor: '#25D36615' }]}>
                            <MaterialCommunityIcons 
                                name="whatsapp" 
                                size={26} 
                                color="#25D366" 
                            />
                        </View>
                        <View style={styles.methodContent}>
                            <Text style={[styles.methodTitle, { color: theme.system.text }]}>
                                {t('profile.changePhone.useWhatsApp')}
                            </Text>
                            <Text style={[styles.methodDescription, { color: theme.system.secondaryText }]}>
                                {t('profile.changePhone.whatsappDesc')}
                            </Text>
                        </View>
                        {loading ? (
                            <MaterialCommunityIcons 
                                name="loading" 
                                size={24} 
                                color="#25D366" 
                            />
                        ) : (
                            <MaterialCommunityIcons 
                                name="chevron-right" 
                                size={22} 
                                color={theme.system.secondaryText} 
                            />
                        )}
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </View>
    );
}

const STATUSBAR_HEIGHT = Platform.OS === 'android' ? Constants.statusBarHeight : 0;

const getStyles = (theme: CustomTheme) => StyleSheet.create({
    safeArea: {
        flex: 1,
        paddingTop: STATUSBAR_HEIGHT,
    },
    container: {
        flex: 1,
    },
    contentContainer: {
        padding: 16,
        paddingBottom: 24,
    },
    header: {
        alignItems: 'center',
    },
    phoneNumber: {
        fontSize: 22,
        fontWeight: '700',
        marginTop: 10,
        marginBottom: 4,
        textAlign: 'center',
    },
    section: {
        marginBottom: 20,
    },
    description: {
        fontSize: 15,
        lineHeight: 22,
        textAlign: 'center',
        marginBottom: 12,
    },
    sectionTitle: {
        fontSize: 17,
        fontWeight: '600',
        marginBottom: 10,
    },
    methodsSection: {
        marginBottom: 16,
    },
    methodContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 10,
        paddingVertical: 10,
        borderRadius: 8,
        backgroundColor: theme.system.background,
        borderWidth: 1,
        borderColor: theme.system.border,
        paddingHorizontal: 12,
    },
    methodIcon: {
        width: 44,
        height: 44,
        borderRadius: 22,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    methodContent: {
        flex: 1,
    },
    methodTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 2,
    },
    methodDescription: {
        fontSize: 13,
        lineHeight: 18,
    },
    errorContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: `${theme.colors.error}15`,
        padding: 10,
        borderRadius: 8,
        marginTop: 8,
    },
    errorText: {
        fontSize: 13,
        marginLeft: 8,
        flex: 1,
    }
}); 