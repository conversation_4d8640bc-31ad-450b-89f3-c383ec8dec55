# Infinite Re-render Fix Testing Guide

## Summary of Fixes Applied

### 1. Theme and Organization Store Circular Dependencies
- **Problem**: `organizationStore.setSelectedOrganization()` directly called `appStyleStore.setTheme()`, creating circular updates
- **Fix**: 
  - Added `isUpdating` flags to prevent concurrent updates
  - Created `setSelectedOrganizationWithTheme()` method for safe theme updates
  - Implemented theme caching to prevent object recreation
  - Added `setThemeWithoutPersist()` for initialization without triggering persistence

### 2. React Query Configuration
- **Problem**: Aggressive refetching and missing stale time settings
- **Fix**:
  - Added 5-minute stale time for queries
  - Disabled `refetchOnWindowFocus` for mobile apps
  - Added intelligent retry logic with exponential backoff
  - Set specific cache times for user profile queries

### 3. useEffect Dependencies and Memoization
- **Problem**: Missing dependencies and object recreation causing infinite loops
- **Fix**:
  - Fixed organization cleanup useEffect with proper dependency tracking
  - Added memoization for theme objects to prevent reference changes
  - Implemented ref-based tracking to prevent duplicate operations

### 4. Error Boundaries
- **Problem**: No graceful handling of infinite re-render loops
- **Fix**:
  - Created `ErrorBoundary` component with infinite loop detection
  - Added automatic recovery for render loops
  - Wrapped main app and dashboard components

## Testing Scenarios

### Scenario 1: Organization Switching
**Test Steps:**
1. Open the app in production build
2. Switch between different organizations multiple times rapidly
3. Verify no "Maximum update depth exceeded" errors occur
4. Check that theme updates correctly without loops

**Expected Result:** Smooth organization switching without crashes

### Scenario 2: Theme Changes
**Test Steps:**
1. Select organizations with different theme colors
2. Navigate between screens while theme is updating
3. Force app to background/foreground during theme changes
4. Verify theme persistence after app restart

**Expected Result:** Theme changes apply correctly without infinite re-renders

### Scenario 3: Authentication State Changes
**Test Steps:**
1. Log in and out multiple times
2. Switch organizations while authenticated/unauthenticated
3. Test with poor network conditions
4. Verify React Query doesn't trigger excessive refetches

**Expected Result:** Authentication changes handled smoothly

### Scenario 4: Production Build Specific Tests
**Test Steps:**
1. Build production version: `eas build --platform android --profile production`
2. Install on physical device
3. Test all scenarios above on production build
4. Monitor for any crashes or performance issues

**Expected Result:** Production build stable without infinite re-renders

## Build Commands for Testing

### Development Build
```bash
npx expo start
```

### Production Build (Android)
```bash
eas build --platform android --profile production
```

### Production Build (iOS)
```bash
eas build --platform ios --profile production
```

## Monitoring and Debugging

### Console Logs to Watch For
- `[appStyleStore] Error setting theme:` - Theme update errors
- `[organizationStore] Error setting organization with theme:` - Organization update errors
- `[Dashboard] Selected organization ... no longer available` - Organization cleanup
- `[ErrorBoundary] Caught an error:` - Error boundary activations
- `[ErrorBoundary] Detected potential infinite re-render loop` - Loop detection

### Performance Monitoring
- Watch for excessive re-renders in React DevTools
- Monitor memory usage during organization switching
- Check for memory leaks in theme cache

## Rollback Plan

If issues persist, revert changes in this order:
1. Remove ErrorBoundary components
2. Revert React Query configuration changes
3. Revert useEffect dependency fixes
4. Revert theme and organization store changes

## Success Criteria

✅ No "Maximum update depth exceeded" errors in production
✅ Smooth organization switching without crashes
✅ Theme changes apply correctly
✅ App remains responsive during state changes
✅ Error boundaries catch and recover from any remaining issues
✅ Production build performs as well as development build

## Additional Recommendations

1. **Monitoring**: Implement crash reporting (e.g., Sentry) to catch any remaining issues
2. **Testing**: Add automated tests for organization switching and theme changes
3. **Performance**: Consider implementing React.memo for expensive components
4. **Caching**: Monitor theme cache size and implement cleanup if needed
