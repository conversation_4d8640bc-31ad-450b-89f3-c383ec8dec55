import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Keyboard,
  TextInput,
} from 'react-native';
import { Button } from 'react-native-paper';
import { Stack, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { appStyleStore } from 'stores/app_style_store';
import OtpInput from '@/login/components/OtpInput'; // Import the new OtpInput component
import { whatsAppTheme } from 'theme/index';
import { CustomDialog } from '@/common_modules/CustomDialog';

// Define types for callback props to ensure clarity
export interface PkceCredentials {
  code_verifier: string;
  state: string;
  // code_challenge and code_challenge_method are usually not needed by verify step
}

export interface BaseVerifyParams {
  phone: string;
  otp: string;
  code_verifier?: string; // Optional as not all flows use PKCE for verify step
  state?: string;         // Optional
  [key: string]: any;   // Allow additional params like display_name, flowId etc.
}

export interface BaseResendParams {
  phone: string;
  code_challenge?: string; // Optional as not all flows use PKCE for resend
  code_challenge_method?: 'S256';
  client_id?: string;
  state?: string;
  phone_otp_channel: 'sms' | 'whatsapp';
  [key: string]: any;   // Allow additional params
}

interface BaseCodeVerificationScreenProps {
  // Core functionality props
  phoneNumber: string;
  verificationMethod: 'sms' | 'whatsapp'; // To determine phone_otp_channel for resend
  codeLength?: number;
  onVerify: (code: string) => Promise<any>; // Simplified: parent handles PKCE/params assembly
  onResendCode: () => Promise<any>;      // Simplified: parent handles PKCE/params assembly
  onSuccessNavigation: string | { pathname: string; params?: Record<string, any> } | (() => void);
  screenStackTitleKey: string;
  
  // UI Customization props
  headerSubtitleKey: string;
  verifyButtonTextKey?: string;
  resendButtonTextKey?: string;
  resendTimerTextKey?: string;
  showDisplayNameInput?: boolean;
  initialDisplayName?: string;
  onDisplayNameChange?: (text: string) => void;
  isNewUserFlow?: boolean; // Helps manage displayName requirement for verify button
  displayNameLabelKey?: string;
  displayNamePlaceholderKey?: string;
}

export default function BaseCodeVerificationScreen({
  phoneNumber,
  verificationMethod,
  codeLength = 6,
  onVerify,
  onResendCode,
  onSuccessNavigation,
  screenStackTitleKey,
  headerSubtitleKey,
  verifyButtonTextKey = 'auth.verify',
  resendButtonTextKey = 'auth.resendCode',
  resendTimerTextKey = 'auth.resendCodeTimer',
  showDisplayNameInput = false,
  initialDisplayName = '',
  onDisplayNameChange,
  isNewUserFlow = false,
  displayNameLabelKey = 'auth.enterDisplayName',
  displayNamePlaceholderKey = 'auth.displayNamePlaceholder',
}: BaseCodeVerificationScreenProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const storeTheme = appStyleStore(state => state.currentThemeName);

  const [verificationCode, setVerificationCode] = useState('');
  const [timer, setTimer] = useState(60);
  const [isTimerRunning, setIsTimerRunning] = useState(true);
  const [verifyLoading, setVerifyLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [displayName, setDisplayName] = useState(initialDisplayName);

  // Attempt tracking and blocking (can be made optional via props if not all screens need it)
  const [attempts, setAttempts] = useState(0);
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockEndTime, setBlockEndTime] = useState<Date | null>(null);
  const BLOCK_DURATION_MINUTES = 5;
  const MAX_ATTEMPTS = 3; // Could be a prop

  // Dialog state for blocked account
  const [showBlockedDialog, setShowBlockedDialog] = useState(false);
  const [blockedDialogMessage, setBlockedDialogMessage] = useState('');

  const otpInputRef = useRef<TextInput>(null);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTimerRunning && timer > 0) {
      interval = setInterval(() => setTimer((prev) => prev - 1), 1000);
    } else if (timer === 0) {
      setIsTimerRunning(false);
    }
    return () => clearInterval(interval);
  }, [timer, isTimerRunning]);

  useEffect(() => {
    const checkBlockStatus = () => {
      if (blockEndTime && new Date() < blockEndTime) {
        setIsBlocked(true);
      } else if (isBlocked && (!blockEndTime || new Date() >= blockEndTime)) {
        setIsBlocked(false);
        setBlockEndTime(null);
        setAttempts(0); // Reset attempts when block expires
      }
    };
    const interval = setInterval(checkBlockStatus, 1000);
    return () => clearInterval(interval);
  }, [blockEndTime, isBlocked]);

  const handleLocalDisplayNameChange = (text: string) => {
    setDisplayName(text);
    if (onDisplayNameChange) {
      onDisplayNameChange(text);
    }
  };

  const handleLocalResendCode = async () => {
    if (isBlocked) {
      const remainingMinutes = Math.ceil((blockEndTime!.getTime() - new Date().getTime()) / 60000);
      setBlockedDialogMessage(t('auth.blockedMessage', { minutes: remainingMinutes }));
      setShowBlockedDialog(true);
      return;
    }
    setResendLoading(true);
    setError(null);
    try {
      const result = await onResendCode();
      
      if (result && result.success) {
        setIsTimerRunning(true);
        setTimer(60);
      } else if (result && !result.success) {
        // Show error from the result
        setError(result.error || t('error.phoneVerification.SYSTEM_ERROR'));
      }
    } catch (err: any) {
      setError(t('error.phoneVerification.SYSTEM_ERROR'));
    } finally {
      setResendLoading(false);
    }
  };

  const handleLocalVerify = async () => {
    if (verificationCode.length !== codeLength) return;
    if (isBlocked) {
      const remainingMinutes = Math.ceil((blockEndTime!.getTime() - new Date().getTime()) / 60000);
      setBlockedDialogMessage(t('auth.blockedMessage', { minutes: remainingMinutes }));
      setShowBlockedDialog(true);
      return;
    }
    if (isNewUserFlow && showDisplayNameInput && !displayName.trim()) {
      setError(t('error.phoneVerification.DISPLAY_NAME_REQUIRED')); 
      return;
    }

    setVerifyLoading(true);
    setError(null);
    try {
      const result = await onVerify(verificationCode);
      
      if (result && result.success) {
        // Success case - reset attempts and remove blocking
        setAttempts(0); 
        setIsBlocked(false);
        setBlockEndTime(null);
        
        // Handle navigation on success
        if (typeof onSuccessNavigation === 'function') {
          onSuccessNavigation();
        } else if (typeof onSuccessNavigation === 'string') {
          router.replace(onSuccessNavigation);
        } else {
          router.replace(onSuccessNavigation);
        }
      } else if (result && !result.success) {
        // Handle error case
        const currentAttempts = attempts + 1;
        setAttempts(currentAttempts);
        
        // Display the error message from the result
        setError(result.error || t('error.phoneVerification.SYSTEM_ERROR'));
        
        // If too many attempts, apply blocking
        if (currentAttempts >= MAX_ATTEMPTS) {
          const newBlockEndTime = new Date();
          newBlockEndTime.setMinutes(newBlockEndTime.getMinutes() + BLOCK_DURATION_MINUTES);
          setBlockEndTime(newBlockEndTime);
          setIsBlocked(true);
          setBlockedDialogMessage(t('auth.blockedMessage', { minutes: BLOCK_DURATION_MINUTES }));
          setShowBlockedDialog(true);
        }
      }
    } catch (err: any) {
      // Handle unexpected errors that weren't caught by the parent component
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(t('error.phoneVerification.SYSTEM_ERROR'));
      const currentAttempts = attempts + 1;
      setAttempts(currentAttempts);
      
      if (currentAttempts >= MAX_ATTEMPTS) {
        const newBlockEndTime = new Date();
        newBlockEndTime.setMinutes(newBlockEndTime.getMinutes() + BLOCK_DURATION_MINUTES);
        setBlockEndTime(newBlockEndTime);
        setIsBlocked(true);
        setBlockedDialogMessage(t('auth.blockedMessage', { minutes: BLOCK_DURATION_MINUTES }));
        setShowBlockedDialog(true);
      }
    } finally {
      setVerifyLoading(false);
    }
  };

  const isVerificationCodeValid = verificationCode.length === codeLength;
  const isSubmitDisabled = 
    !isVerificationCodeValid || 
    (isNewUserFlow && showDisplayNameInput && !displayName.trim()) || 
    verifyLoading || 
    isBlocked;

  const styles = getThemedStyles();

  return (
    <>
      <Stack.Screen options={{ headerTitle: t(screenStackTitleKey) }} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
          <View style={styles.contentContainer}>
            <View style={styles.header}>
              <View style={styles.phoneDisplay}>
                <Text style={[styles.phoneDisplayText, { color: whatsAppTheme.system.text }]}>{phoneNumber}</Text>
              </View>
              <Text style={[styles.subtitle, { color: whatsAppTheme.system.secondaryText }]}>{t(headerSubtitleKey)}</Text>
            </View>

            <View style={styles.inputContainer}>
              <OtpInput
                codeLength={codeLength}
                verificationCode={verificationCode}
                onVerificationCodeChange={setVerificationCode}
                onSubmit={handleLocalVerify} // Auto-submit on full code
                inputRef={otpInputRef}
                verificationMethod={verificationMethod}
              />

              {showDisplayNameInput && (
                <View style={styles.displayNameContainer}>
                  <Text style={[styles.displayNameLabel, { color: whatsAppTheme.system.secondaryText }]}>
                    {t(displayNameLabelKey)}
                  </Text>
                  <TextInput
                    style={[
                      styles.displayNameInput,
                      { borderColor: whatsAppTheme.system.border, color: whatsAppTheme.system.text },
                    ]}
                    value={displayName}
                    onChangeText={handleLocalDisplayNameChange}
                    placeholder={t(displayNamePlaceholderKey)}
                    placeholderTextColor={whatsAppTheme.system.secondaryText}
                  />
                </View>
              )}

              {isBlocked && blockEndTime && (
                <Text style={[styles.blockedText, { color: whatsAppTheme.colors.error }]}>
                  {t('auth.blockedCountdown', { minutes: Math.ceil((blockEndTime.getTime() - new Date().getTime()) / 60000) })}
                </Text>
              )}

              {error && <Text style={[styles.errorText, { color: whatsAppTheme.colors.error }]}>{error}</Text>}

              <Button
                mode="text"
                onPress={handleLocalResendCode}
                disabled={resendLoading || isTimerRunning || isBlocked}
                style={styles.resendButton}
                labelStyle={[styles.resendText, { color: whatsAppTheme.colors.primary }]}
              >
                {isTimerRunning ? t(resendTimerTextKey, { seconds: timer }) : t(resendButtonTextKey)}
              </Button>

              <Button
                mode="contained"
                onPress={handleLocalVerify}
                disabled={isSubmitDisabled}
                style={[
                  styles.button,
                  { backgroundColor: isSubmitDisabled ? whatsAppTheme.colors.primaryDisabled : whatsAppTheme.colors.primary },
                ]}
                contentStyle={styles.buttonContent}
                labelStyle={[
                  styles.buttonLabel,
                  isSubmitDisabled && { color: 'white' }, 
                ]}
                loading={verifyLoading && !isBlocked}
              >
                {t(verifyButtonTextKey)}
              </Button>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
      
      <CustomDialog
        visible={showBlockedDialog}
        title={t('auth.blockedTitle')}
        message={blockedDialogMessage}
        type="warning"
        confirmText={t('common.ok')}
        onConfirm={() => setShowBlockedDialog(false)}
      />
    </>
  );
}

function getThemedStyles() {
  return StyleSheet.create({
    container: {
      flex: 1,
      padding: 24,
    },
    contentContainer: {
      flex: 1,
      width: '100%',
    },
    header: {
      alignItems: 'center',
      marginTop: 20,
      marginBottom: 40,
    },
    phoneDisplay: {
      marginBottom: 16,
    },
    phoneDisplayText: {
      fontSize: 20,
      fontWeight: '600',
    },
    subtitle: {
      fontSize: 16,
      textAlign: 'center',
    },
    inputContainer: {
      alignItems: 'center',
    },
    displayNameContainer: {
      width: '100%',
      marginBottom: 20,
    },
    displayNameLabel: {
      fontSize: 14,
      marginBottom: 8,
    },
    displayNameInput: {
      width: '100%',
      height: 48,
      borderWidth: 2,
      borderRadius: 8,
      paddingHorizontal: 12,
      fontSize: 16,
    },
    errorText: {
      fontSize: 14,
      marginBottom: 16,
      textAlign: 'center',
    },
    blockedText: {
      fontSize: 14,
      marginBottom: 16,
      textAlign: 'center',
      fontWeight: '500',
    },
    resendButton: {
      marginBottom: 24,
    },
    resendText: {
      fontSize: 14,
    },
    button: {
      borderRadius: 12,
      width: Platform.OS === 'web' ? 340 : '100%',
      height: 48,
      alignSelf: 'center',
    },
    buttonContent: {
      height: 48,
    },
    buttonLabel: {
      fontSize: 16,
      fontWeight: '600',
      color: '#FFFFFF',
    },
  });
} 