import React, { useState, useEffect, useLayoutEffect, useCallback, useRef, useMemo } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    Image,
    Dimensions,
    TouchableOpacity,
    Platform,
    Linking,
    Share,
    Alert,
    ActivityIndicator,
    Modal,
    FlatList,
    StatusBar,
    Animated,
    ViewToken,
    TouchableWithoutFeedback,
    KeyboardAvoidingView,
} from 'react-native';
import { VideoView, useVideoPlayer } from 'expo-video';
import { useTranslation } from 'react-i18next';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter, useLocalSearchParams, Stack, useNavigation } from 'expo-router';
import { ErrorView } from '@/common_modules/ErrorView';
import { TextDialog } from '@/common_modules/TextDialog';
import { parseISO, format, isPast, Locale } from 'date-fns';
import { enUS, zhCN } from 'date-fns/locale';
import { toZonedTime } from 'date-fns-tz';
import { generateShareContent } from 'utils/shareUtils';
import { RichTextDescription } from '@/common_modules/RichTextDescription';
import { getValidImageUrl } from 'utils/imageUtils';
import { appStyleStore } from 'stores/app_style_store';
import { authenticationStore } from 'stores/authentication_store';
import { userProfileStore } from 'stores/user_store';
import { organizationStore } from 'stores/organization_store';
import { 
    useFetchEventDetails,
} from '@/api/public_events_services';
import { 
    useFetchUserEventVolunteerApplicationsList,
    useApplyEventVolunteer, 
    useWithdrawEventVolunteer, 
} from '@/api/volunteer_services';
import { useFetchUserVolunteerQualifications } from '@/api/user_services';
import { useCancelEventRegistration, useRegisterForEvent } from '@/api/user_events_services';
import { useFetchGovernmentFundingTypes } from '@/api/utils_services';
import {  
    MediaItemPayload, 
    TypesPayload
} from '@/api/api_config'; 
import { VerificationTypeEnum } from 'types/enums';
import { createTheme } from 'theme/index';
import { useFetchRegisteredEventsList } from '@/api/user_events_services';
import EventActionButtons from './components/EventActionButtons';
import ImageCarousel from './components/ImageCarousel';
import ImageViewer from './components/ImageViewer';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Add locale and date formatting functions
const getLocale = (language: string): Locale => {
  switch (language.toLowerCase().split('-')[0]) {
    case 'zh':
      return zhCN;
    default:
      return enUS;
  }
};

const formatEventDate = (dateString: string | undefined, language: string): string => {
  if (!dateString) return '';
  try {
    const timeZone = 'Asia/Hong_Kong';
    const parsedDate = parseISO(dateString);
    const zonedDate = toZonedTime(parsedDate, timeZone);
    return format(zonedDate, 'PPP', { locale: getLocale(language) });
  } catch (error) {
    console.warn('Error formatting date:', error);
    return '';
  }
};

const formatEventTime = (dateString: string | undefined, language: string): string => {
  if (!dateString) return '';
  try {
    const timeZone = 'Asia/Hong_Kong';
    const parsedDate = parseISO(dateString);
    const zonedDate = toZonedTime(parsedDate, timeZone);
    return format(zonedDate, 'HH:mm', { locale: getLocale(language) });
  } catch (error) {
    console.warn('Error formatting time:', error);
    return '';
  }
};

interface HighlightItemProps {
    label: string;
    value: string | number;
}

interface AttachmentItemProps {
    attachment: {
        file_path: string;
        file_size: number;
    };
    theme: any;
    styles: any;
    t: (key: string) => string;
    openVideoModal: (url: string) => void;
    router: any;
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FFFFFF',
    },
    scrollView: {
        flex: 1,
    },

    content: {
        flex: 1,
        backgroundColor: '#FFFFFF',
        borderTopLeftRadius: 24,
        borderTopRightRadius: 24,
        marginTop: -24,
        paddingTop: 24,
        paddingHorizontal: 20,
        minHeight: SCREEN_HEIGHT * 0.7,
    },
    highlights: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 16,
        marginVertical: 20,
        backgroundColor: '#FAFAFA',
        borderRadius: 16,
    },
    highlightItem: {
        flex: 1,
        alignItems: 'center',
    },
    highlightDivider: {
        width: 1,
        height: 40,
        backgroundColor: '#F0F0F0',
    },
    highlightLabel: {
        fontSize: 13,
        color: '#666666',
        marginTop: 4,
    },
    highlightValue: {
        fontSize: 15,
        color: '#333333',
        fontWeight: '600',
        marginBottom: 2,
    },
    highlightValueSmall: {
        fontSize: 13,
        color: '#666666',
        fontWeight: '400',
    },
    highlightValueWrapper: {
        flexDirection: 'row',
        alignItems: 'baseline',
        gap: 4,
    },
    infoSection: {
        marginBottom: 24,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333333',
        marginBottom: 12,
    },
    locationTitle: {
        marginTop: 16,
    },
    CtaText: {
        fontSize: 14,
        color: '#666666',
        marginTop: 4,
    },
    infoCard: {
        backgroundColor: '#FAFAFA',
        borderRadius: 16,
        padding: 16,
    },
    infoContent: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    infoTextContainer: {
        flex: 1,
        marginLeft: 12,
        marginRight: 8,
    },
    infoText: {
        fontSize: 15,
        color: '#333333',
        fontWeight: '500',
    },
    detailSection: {
        marginBottom: 24,
    },
    detailTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333333',
        marginBottom: 12,
    },
    detailContent: {
        fontSize: 15,
        lineHeight: 24,
        color: '#333333',
        paddingLeft: 4,
    },
    footer: {
        backgroundColor: '#FFFFFF',
        borderTopWidth: 1,
        borderTopColor: '#F0F0F0',
    },
    footerContent: {
        flexDirection: 'row',
        gap: 12,
        paddingHorizontal: 16,
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: '#F0F0F0', // Use hardcoded color since styles is outside component
        paddingBottom: 36,
    },
    registerButton: {
        flex: 1,
        borderRadius: 8,
        marginHorizontal: 4,
    },
    volunteerButton: {
        borderColor: '#FF1493',
        backgroundColor: 'transparent',
    },
    registerButtonContent: {
        height: 50,
    },
    registerButtonLabel: {
        fontSize: 16,
        fontWeight: '600',
    },
    volunteerButtonLabel: {
        color: '#FF1493',
    },
    volunteerButtonContent: {
        backgroundColor: 'transparent',
    },
    volunteerButtonContentPressed: {
        backgroundColor: '#FF1493',
    },
    errorContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
        backgroundColor: '#FFFFFF',
    },
    errorTitle: {
        fontSize: 20,
        fontWeight: '600',
        color: '#333333',
        marginTop: 16,
        marginBottom: 8,
    },
    errorText: {
        fontSize: 16,
        color: '#666666',
        textAlign: 'center',
        marginBottom: 24,
    },
    errorButton: {
        minWidth: 120,
    },
    imageErrorOverlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0, 0, 0, 0.75)',
        alignItems: 'center',
        justifyContent: 'center',
    },
    imageErrorText: {
        color: '#FFFFFF',
        fontSize: 14,
        marginTop: 8,
    },
    headerRight: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginRight: 8,
    },
    headerButton: {
        padding: 8,
    },
    headerSection: {
        paddingBottom: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    categoryRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
        marginBottom: 8,
    },
    category: {
        fontSize: 12,
        fontWeight: '600',
        letterSpacing: 1,
    },
    titleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    title: {
        fontSize: 28,
        fontWeight: '700',
        color: '#333333',
        flexShrink: 1,
    },
    sponsorLogo: {
        width: 32,
        height: 32,
        borderRadius: 4,
        marginRight: 10,
    },
    organizerRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginTop: 12,
        gap: 8,
    },
    organizerText: {
        fontSize: 14,
        color: '#666666',
        flex: 1,
        paddingRight: 16,
        lineHeight: 20,
    },
    eventEndedTag: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F5F5F5',
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 4,
        gap: 4,
    },
    eventEndedTagText: {
        fontSize: 12,
        fontWeight: '500',
        color: '#666666',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    videoContainer: {
        height: SCREEN_HEIGHT * 0.35,
        width: SCREEN_WIDTH,
        backgroundColor: '#000000',
    },
    videoThumbnail: {
        width: '100%',
        height: '100%',
        resizeMode: 'cover',
    },
    playButton: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    eventDetailsCard: {
        paddingHorizontal: 16,
    },
    eventDetailRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    eventDetailLastRow: {
        borderBottomWidth: 0,
    },
    eventDetailIcon: {
        width: 32,
        alignItems: 'center',
    },
    eventDetailContent: {
        flex: 1,
        marginLeft: 12,
    },
    eventDetailLabel: {
        fontSize: 14,
        color: '#666666',
        marginBottom: 2,
    },
    eventDetailValue: {
        fontSize: 16,
        color: '#333333',
        fontWeight: '500',
    },

    closeButton: {
        marginLeft: 10,
        padding: 5,
    },
    identitySection: {
        marginBottom: 24,
    },
    identityTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333333',
        marginBottom: 12,
    },
    identityDescription: {
        fontSize: 15,
        lineHeight: 24,
        color: '#333333',
        paddingLeft: 4,
    },
    attachmentItem: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
        width: '100%',
    },
    attachmentIconContainer: {
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    attachmentInfo: {
        flex: 1,
        marginLeft: 10,
    },
    attachmentName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333333',
    },
    attachmentSize: {
        fontSize: 14,
        color: '#666666',
    },
    attachmentsContainer: {
        flexDirection: 'column',
        width: '100%',
    },
    // Add new styles for government funding section
    governmentFundingSection: {
        paddingBottom: 16,
    },
    governmentFundingTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333333',
        marginBottom: 16,
    },
    fundingItemContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    fundingLogo: {
        width: 120,
        height: 60,
        resizeMode: 'contain',
        marginRight: 16,
    },
    fundingName: {
        fontSize: 16,
        fontWeight: '500',
        color: '#333333',
        flex: 1,
        flexWrap: 'wrap',
    },
});

// Add utility function for formatting file size
const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

// Check if file is an image based on extension
const isImageFile = (fileName: string | undefined): boolean => {
    if (!fileName) return false;
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'heic', 'heif'];
    return imageTypes.includes(extension);
};



// Get video attachments (non-image files, assumed to be videos)
const getAttachments = (mediaItems: MediaItemPayload[] | undefined): MediaItemPayload[] => {
    if (!mediaItems) return [];
    return mediaItems.filter(item => !isImageFile(item.file_name));
};

// Helper function to map event verification key to enum
const mapEventVerificationKeyToEnum = (key: string): VerificationTypeEnum | null => {
    const keyMap: { [key: string]: VerificationTypeEnum } = {
        'hk_id_card': VerificationTypeEnum.HkIDCard,
        'mainland_china_id_card': VerificationTypeEnum.MainlandChinaIDCard,
        'mainland_travel_permit': VerificationTypeEnum.MainlandTravelPermit,
        'passport': VerificationTypeEnum.Passport,
        'hk_youth_plus': VerificationTypeEnum.HkYouthPlus,
        'address_proof': VerificationTypeEnum.AddressProof,
        'student_id': VerificationTypeEnum.StudentID,
        'home_visit': VerificationTypeEnum.HomeVisit,
        // Add other mappings as necessary
    };
    return keyMap[key.toLowerCase()] || null;
};

export default function EventDetailsScreen() {
    const storeTheme = appStyleStore(state => state.theme);
    const theme = storeTheme || createTheme('red');
    const router = useRouter();
    const params = useLocalSearchParams<{ eventId?: string; orgId?: string }>();
    const navigation = useNavigation();
    const { t, i18n } = useTranslation();

    // Authentication and User Profile
    const isAuthenticated = authenticationStore(state => state.isAuthenticated);
    const currentUser = userProfileStore(state => state.profile);
    const userVerifications = currentUser?.verification_status || {};
    const { selectedOrganization: selectedOrgId } = organizationStore(state => state);
    
    // API Hooks
    const eventId = params.eventId;
    const { data: eventDataResponse, isLoading: isLoadingEvent, error: eventError, refetch: refetchEventDetails } = useFetchEventDetails({ eventId: eventId! });
    const event = eventDataResponse;
    
    // User Volunteer Applications for this event
    const userIdForVolunteerHook = isAuthenticated ? currentUser?.id : undefined;
    const { data: userEventVolunteerApplicationsData, isLoading: isLoadingUserEventVolunteerApps, refetch: refetchUserEventVolunteerApps } = useFetchUserEventVolunteerApplicationsList(
        { user_id: userIdForVolunteerHook, event_id: eventId, limit: 1, offset: 0 } 
    );
    
    // User Event Registration for this specific event
    const { data: userEventRegistrationsData, isLoading: isLoadingUserEventRegistrations, refetch: refetchUserEventRegistrations } = useFetchRegisteredEventsList(
        isAuthenticated && eventId ? { 
            event_id: eventId, 
            limit: 1, 
            offset: 0 
        } : undefined
    );
    
    const { data: orgVolunteerStatusData, isLoading: isLoadingOrgVolunteerStatus, refetch: refetchOrgVolunteerStatus } = useFetchUserVolunteerQualifications(
        {
            enabled: isAuthenticated && !!event?.organization_id && !!currentUser?.id,
        }
    );
    
    // Mutations
    const { mutateAsync: applyAsVolunteerMutation, isPending: isApplyingAsVolunteer } = useApplyEventVolunteer();
    const { mutateAsync: registerAsParticipantMutation, isPending: isRegisteringAsParticipant } = useRegisterForEvent();
    const { mutateAsync: withdrawVolunteerApplicationMutation, isPending: isWithdrawingVolunteerApp } = useWithdrawEventVolunteer();
    const { mutateAsync: cancelEventRegistrationMutation, isPending: isCancellingRegistration } = useCancelEventRegistration();

    // === 状态管理分组 ===
    
    // UI 状态
    const [hasError, setHasError] = useState(false);
    
    // 其他对话框状态 - 专门的错误处理状态
    const [fileErrorDialogVisible, setFileErrorDialogVisible] = useState(false);
    const [fileErrorMessage, setFileErrorMessage] = useState('');
    const [error, setError] = useState<any>(null);
    
    // 媒体相关状态
    const [imageViewerVisible, setImageViewerVisible] = useState(false);
    const [imageViewerInitialIndex, setImageViewerInitialIndex] = useState(0);
    const [isVideoModalVisible, setVideoModalVisible] = useState(false);
    const [currentVideoUrl, setCurrentVideoUrl] = useState<string | null>(null);
    
    // 用户状态
    const [isParticipantActive, setIsParticipantActive] = useState(false);
    const [isVolunteerActive, setIsVolunteerActive] = useState(false);

    // Other hooks
    const player = useVideoPlayer(currentVideoUrl!, (_player) => { });
    
    // Derived data
    const eventVolunteerApplication = userEventVolunteerApplicationsData?.[0];
    
    // Add government funding types state and API hook
    // Memoize the request params to prevent unnecessary re-renders
    const governmentFundingTypesParams = useMemo(() => ({ 
        lang_code: i18n.language.replace('-', '_') 
    }), [i18n.language]);
    
    const { data: governmentFundingTypesData, isLoading: isLoadingGovernmentFundingTypes, refetch: refetchGovernmentFundingTypes } = useFetchGovernmentFundingTypes(governmentFundingTypesParams);

    // === 身份验证和验证逻辑 ===
    
    // Helper function to map event verification key to enum - moved to EventActionButtons
    const mapEventVerificationKeyToEnum = (key: string): VerificationTypeEnum | null => {
        const keyMap: { [key: string]: VerificationTypeEnum } = {
            'hk_id_card': VerificationTypeEnum.HkIDCard,
            'mainland_china_id_card': VerificationTypeEnum.MainlandChinaIDCard,
            'mainland_travel_permit': VerificationTypeEnum.MainlandTravelPermit,
            'passport': VerificationTypeEnum.Passport,
            'hk_youth_plus': VerificationTypeEnum.HkYouthPlus,
            'address_proof': VerificationTypeEnum.AddressProof,
            'student_id': VerificationTypeEnum.StudentID,
            'home_visit': VerificationTypeEnum.HomeVisit,
        };
        return keyMap[key.toLowerCase()] || null;
    };

    const updateVideoSource = useCallback((url: string | null) => {
        if (url) {
            player.replaceAsync(url).then(() => {
                player.play();
            });
        }
    }, [player]);

    const fetchAllDetails = useCallback(async () => {
        if (event) {
            const participantActiveStatuses = ['approved', 'pending_approval', 'waitlisted', 'pending_payment', 'confirmed', 'registered'];
            const volunteerActiveStatuses = ['approved', 'pending', 'pending_approval', 'confirmed', 'applied'];

            // Check participant status from userEventRegistrationsData first, then fallback to event data
            const currentUserRegistration = userEventRegistrationsData?.[0];
            if (currentUserRegistration?.status) {
                setIsParticipantActive(
                    participantActiveStatuses.includes(currentUserRegistration.status)
                );
            } else if (event.current_user_registration_status) {
                setIsParticipantActive(
                    participantActiveStatuses.includes(event.current_user_registration_status)
                );
            } else {
                setIsParticipantActive(false);
            }

            if (eventVolunteerApplication?.status) {
                 setIsVolunteerActive(volunteerActiveStatuses.includes(eventVolunteerApplication.status));
            } else if (event.current_user_volunteer_status) { 
                 setIsVolunteerActive(volunteerActiveStatuses.includes(event.current_user_volunteer_status));
            } else {
                setIsVolunteerActive(false);
            }
        }
    }, [event, eventVolunteerApplication, userEventRegistrationsData]);

    const handleShare = useCallback(async () => {
        if (!event) return;
        try {
            const shareContent = generateShareContent({
                event: {
                    id: event.id,
                    title: event.title,
                    date: event.start_time,
                    endDate: event.end_time, // Add end date for time period
                    location: event.location_full_address || event.location_online_url || '',
                    price: event.price ? parseFloat(event.price) : 0, // Convert string to number
                    mapLink: event.location_full_address ? `https://maps.google.com/maps?q=${encodeURIComponent(event.location_full_address)}` : undefined, // Add map link
                },
                t,
                language: i18n.language
            });
            await Share.share(shareContent);
        } catch (error) {
            console.error('Share error:', error);
        }
    }, [event, t, i18n.language]);

    // Utility functions

    // Success and notification dialogs moved to EventActionButtons component

    const openImageViewer = (index: number = 0) => {
        setImageViewerInitialIndex(index);
        setImageViewerVisible(true);
    };

    const closeImageViewer = () => {
        setImageViewerVisible(false);
    };

    // useLayoutEffect hook
    useLayoutEffect(() => {
        navigation.setOptions({
            title: t('events.detail.title'),
        });
    }, [navigation, t, i18n.language]);

    // useEffect hooks
    useEffect(() => {
        fetchAllDetails();
    }, [fetchAllDetails]);


    useEffect(() => {
        if (event?.organization_id && isAuthenticated && currentUser?.id) {
            refetchOrgVolunteerStatus();
        }
    }, [event?.organization_id, isAuthenticated, currentUser?.id, refetchOrgVolunteerStatus]);

    useEffect(() => {
        if (currentUser?.id && eventId && isAuthenticated) {
            refetchUserEventVolunteerApps();
            refetchUserEventRegistrations();
        }
    }, [currentUser?.id, eventId, isAuthenticated, refetchUserEventVolunteerApps, refetchUserEventRegistrations]);

    // NOW HANDLE LOADING AND ERROR STATES AFTER ALL HOOKS
    if (isLoadingEvent || 
        (isAuthenticated && !!currentUser?.id && !!eventId && isLoadingUserEventVolunteerApps) || 
        (isAuthenticated && !!event?.organization_id && !!currentUser?.id && isLoadingOrgVolunteerStatus) ||
        (isAuthenticated && !!eventId && isLoadingUserEventRegistrations)) {  
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={theme.colors.primary} />
            </View>
        );
    }

    if (!event || eventError) {
        return (
            <ErrorView
                onGoBack={() => router.back()}
            />
        );
    }

    const handleGetDirections = () => {
        if (!event) return;
        const location = event.location_full_address || event.location_online_url || '';

        // Use Alert.alert for map selection
        if (Platform.OS === 'ios') {
            Alert.alert(
                t('events.detail.openMap'),
                t('events.detail.chooseMapApp'),
                [
                    {
                        text: t('events.detail.appleMapApp'),
                        onPress: () => Linking.openURL(`maps:0,0?q=${location}`)
                    },
                    {
                        text: t('events.detail.googleMapApp'),
                        onPress: () => Linking.openURL(`comgooglemaps://?q=${location}`)
                    },
                    {
                        text: t('common.cancel'),
                        style: 'cancel'
                    }
                ]
            );
        } else {
            // On Android, just open the default maps app
            Linking.openURL(`geo:0,0?q=${location}`);
        }
    };

    const HighlightItem: React.FC<HighlightItemProps> = ({ label, value }) => {
        // Handle numeric display for attendees and waitlist
        if (label === t('events.detail.attendees') || label === t('events.detail.maxWaitingList')) {
            // Extract current/limit from value string like "0/50"
            const parts = value.toString().split('/');
            const current = parts[0] || '0';
            const limit = parts[1] || '0';
            
            return (
                <View style={styles.highlightItem}>
                    <View style={styles.highlightValueWrapper}>
                        <Text style={styles.highlightValue}>{current}</Text>
                        <Text style={styles.highlightValueSmall}>/ {limit}</Text>
                    </View>
                    <Text style={styles.highlightLabel}>{label}</Text>
                </View>
            );
        }

        return (
            <View style={styles.highlightItem}>
                <Text style={styles.highlightValue}>{value}</Text>
                <Text style={styles.highlightLabel}>{label}</Text>
            </View>
        );
    };

    const DetailSection = ({ title, content }: { title: string; content: string }) => (
        <View style={styles.detailSection}>
            <Text style={styles.detailTitle}>{title}</Text>
            <Text style={styles.detailContent}>{content}</Text>
        </View>
    );
    // 格式化價格顯示
    const formatPrice = (price: number) => {
        if (price === 0) {
            return t('events.detail.free');
        }
        return `HK$${price.toFixed(2)}`;
    };

    // Registration handling functions moved to EventActionButtons component

    // Placeholder for sponsor logo - replace with actual source
    const sponsorLogoUri = 'https://via.placeholder.com/50x50.png?text=Logo';

    const AttachmentItem = ({ attachment, theme, styles, t, openVideoModal, router }: AttachmentItemProps) => {
        const handlePress = () => {
            const validUrl = getValidImageUrl(attachment.file_path);
            if (!validUrl) {
                setFileErrorMessage(t('common.fileOpenError'));
                setFileErrorDialogVisible(true);
                return;
            }

            // Since all attachments are videos, directly open video modal
            openVideoModal(validUrl);
        };

        const displayFileName = typeof attachment.file_path === 'string'
            ? attachment.file_path.split('/').pop() || t('common.unknown')
            : t('common.unknown');

        return (
            <TouchableOpacity
                style={styles.attachmentItem}
                onPress={handlePress}
            >
                <View style={styles.attachmentIconContainer}>
                    <MaterialCommunityIcons name="video" size={24} color="#FF5252" />
                </View>
                <View style={styles.attachmentInfo}>
                    <Text style={styles.attachmentName} numberOfLines={1}>
                        {displayFileName}
                    </Text>
                    <Text style={styles.attachmentSize}>
                        {formatFileSize(attachment.file_size)}
                    </Text>
                </View>
                <MaterialCommunityIcons
                    name="play-circle"
                    size={24}
                    color={theme.colors.primary}
                />
            </TouchableOpacity>
        );
    };

    const verificationKeys = event?.verification_type_keys;

    const canVolunteerForEventOrg = (): boolean => {
        if (!isAuthenticated || !event?.organization_id) return false; // User must be logged in & event must have org

        const currentOrgQualification = orgVolunteerStatusData?.find(
            q => q.organization_id === event.organization_id
        );

        return currentOrgQualification?.status === 'approved';
    };

    // All event action handling functions moved to EventActionButtons component

    // Footer buttons logic moved to EventActionButtons component

    // Replace the renderGovernmentFundingSection function with this updated version
    const renderGovernmentFundingSection = () => {
        if (!event?.government_funding_keys || event.government_funding_keys.length === 0) {
            return null;
        }

        // Extract funding keys based on the type of government_funding_keys
        const getFundingKeysFromData = () => {
            if (!Array.isArray(event.government_funding_keys)) {
                // Handle single item case
                return [typeof event.government_funding_keys === 'string'
                    ? event.government_funding_keys
                    : (event.government_funding_keys as any).slug || ''];
            }

            // Handle array case - could be array of strings or objects
            return event.government_funding_keys.map((item: any) => {
                if (typeof item === 'string') return item;
                return item.slug || ''; // Extract slug if it's an object
            });
        };

        const fundingKeys = getFundingKeysFromData();

        // Filter the governmentFundingTypes to only include those that match the keys in the event
        const matchedFundingTypes = (governmentFundingTypesData as any)?.filter((type: TypesPayload) => {
            return fundingKeys.includes(typeof type.key === 'string' ? type.key : '');
        }) || []; // Add fallback to empty array

        if (matchedFundingTypes.length !== 0 && !isLoadingGovernmentFundingTypes) {
            return (
                <View style={styles.governmentFundingSection}>
                    <Text style={styles.governmentFundingTitle}>
                        {t('events.detail.governmentFunding', 'Government Funding')}
                    </Text>

                    {isLoadingGovernmentFundingTypes ? (
                        <ActivityIndicator size="small" color={theme.colors.primary} />
                    ) : (
                        matchedFundingTypes.map((fundingType: TypesPayload) => {
                            // Attempt to get an image for this funding type
                            let logoSource = null;
                            const keyValue = typeof fundingType.key === 'string' ? fundingType.key : '';
                            try {
                                switch (keyValue) {
                                    case 'gov_funded_prog':
                                        logoSource = require('@/assets/government-funding-logo/gov_funded_prog.png');
                                        break;
                                    case 'hksar_hyab':
                                        logoSource = require('@/assets/government-funding-logo/hksar_hyab.png');
                                        break;
                                    case 'youth_dev_commission':
                                        logoSource = require('@/assets/government-funding-logo/youth_dev_commission.png');
                                        break;
                                    case 'hk_youth_plus':
                                        logoSource = require('@/assets/government-funding-logo/hk_youth_plus.png');
                                        break;
                                }
                            } catch (error) {
                                console.warn('Failed to load funding image for', keyValue, error);
                            }

                            return (
                                <View key={fundingType.key} style={styles.fundingItemContainer}>
                                    {logoSource && (
                                        <Image
                                            source={logoSource}
                                            style={styles.fundingLogo}
                                        />
                                    )}
                                    <Text style={styles.fundingName}>
                                        {typeof fundingType.name === 'string' ? fundingType.name : keyValue}
                                    </Text>
                                </View>
                            );
                        })
                    )}
                </View>
            );
        } else {
            return null;
        }
    };

    return (
        <>
            <Stack.Screen
                options={{
                    headerShown: true,
                    headerTitleAlign: 'center',
                    headerTitle: t('events.detail.title'),
                    headerShadowVisible: false,
                    headerRight: () => (
                        <View style={styles.headerRight}>
                            <TouchableOpacity onPress={handleShare} style={styles.headerButton}>
                                <MaterialCommunityIcons name="share-variant" size={24} color={theme.system.text} />
                            </TouchableOpacity>
                        </View>
                    )
                }}
            />
            <View style={[styles.container, { backgroundColor: theme.system.background }]}>
                <ScrollView
                    style={styles.scrollView}
                    bounces={false}
                    showsVerticalScrollIndicator={false}
                >
                    <ImageCarousel
                        mediaItems={event?.media_items || []}
                        onImagePress={openImageViewer}
                        theme={theme}
                    />

                    <View style={[styles.content, { backgroundColor: theme.system.background }]}>
                        <View style={styles.headerSection}>
                            <View style={styles.categoryRow}>
                                <Text style={[styles.category, { color: theme.colors.primary }]}>
                                    {event.tags?.[0] 
                                        ? `${(event.tags[0] as any).name_en?.toUpperCase() || 'EVENT'} / ${(event.tags[0] as any).name_zh_hk || '活動'}`
                                        : 'EVENT / 活動'
                                    }
                                </Text>

                                {isPast(parseISO(event.end_time || event.start_time)) && (
                                    <View style={styles.eventEndedTag}>
                                        <MaterialCommunityIcons name="clock-outline" size={14} color="#666666" />
                                        <Text style={styles.eventEndedTagText}>{t('events.detail.eventEnded')}</Text>
                                    </View>
                                )}
                            </View>
                            <Text style={styles.title}>{event.title}</Text>
                            <View style={styles.organizerRow}>
                                <Text style={[styles.organizerText, { color: theme.system.secondaryText }]}>
                                {t('events.detail.organizer')}: {event.organization_name}
                                </Text>
                            </View>
                        </View>


                        <View style={styles.highlights}>
                            <HighlightItem
                                label={t('events.detail.price')}
                                value={formatPrice(parseFloat(event.price || '0'))}
                            />
                            <View style={styles.highlightDivider} />
                            <HighlightItem
                                label={t('events.detail.attendees')}
                                value={`${event.registered_count || 0}/${event.participant_limit || 0}`}
                            />
                            <View style={styles.highlightDivider} />
                            <HighlightItem
                                label={t('events.detail.maxWaitingList')}
                                value={`${event.waitlisted_count || 0}/${event.waitlist_limit || 0}`}
                            />
                        </View>

                        {/* Government Funding Section */}
                        {renderGovernmentFundingSection()}

                        <View style={styles.infoSection}>
                            <Text style={styles.sectionTitle}>{t('events.detail.dateAndTime')}</Text>
                            <View style={styles.infoCard}>
                                <View style={styles.infoContent}>
                                    <MaterialCommunityIcons name="calendar-start" size={24} color={theme.colors.primary} />
                                    <View style={styles.infoTextContainer}>
                                        <Text style={styles.eventDetailLabel}>{t('events.detail.startDateTime')}</Text>
                                        <Text style={styles.infoText}>
                                            {formatEventDate(event.start_time, i18n.language)} {formatEventTime(event.start_time, i18n.language)}
                                        </Text>
                                    </View>
                                </View>
                            </View>
                            {event.end_time && (
                                <View style={[styles.infoCard, { marginTop: 8 }]}>
                                    <View style={styles.infoContent}>
                                        <MaterialCommunityIcons name="calendar-end" size={24} color={theme.colors.primary} />
                                        <View style={styles.infoTextContainer}>
                                            <Text style={styles.eventDetailLabel}>{t('events.detail.endDateTime')}</Text>
                                            <Text style={styles.infoText}>
                                                {formatEventDate(event.end_time, i18n.language)} {formatEventTime(event.end_time, i18n.language)}
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                            )}

                            <Text style={[styles.sectionTitle, styles.locationTitle]}>{t('events.detail.location')}</Text>
                            <TouchableOpacity style={styles.infoCard} onPress={handleGetDirections}>
                                <View style={styles.infoContent}>
                                    <MaterialCommunityIcons name="map-marker" size={24} color={theme.colors.primary} />
                                    <View style={styles.infoTextContainer}>
                                        <Text style={styles.infoText}>{event.location_full_address || event.location_online_url || ''}</Text>
                                        <Text style={styles.CtaText}>
                                            {t('events.detail.openToMapApp')}
                                        </Text>
                                    </View>
                                    <MaterialCommunityIcons name="chevron-right" size={24} color="#666666" />
                                </View>
                            </TouchableOpacity>
                        </View>



                        <View style={styles.detailSection}>
                            <Text style={styles.detailTitle}>{t('events.detail.description')}</Text>
                            <RichTextDescription description={event?.jsonContent} />
                        </View>

                        {verificationKeys && verificationKeys.length > 0 && (
                            <View style={styles.infoSection}>
                                <Text style={styles.sectionTitle}>{t('events.detail.thingsToKnow')}</Text>
                                <View style={styles.eventDetailsCard}>
                                    {verificationKeys.map((req: string, index: number) => (
                                        <View
                                            key={req}
                                            style={[
                                                styles.eventDetailRow,
                                                index === verificationKeys.length - 1 && styles.eventDetailLastRow
                                            ]}
                                        >
                                            <View style={styles.eventDetailIcon}>
                                                <MaterialCommunityIcons
                                                    name={req === 'hk_id_card' ? 'card-account-details' :
                                                        req === 'mainland_china_id_card' ? 'card-account-details-outline' :
                                                            req === 'mainland_travel_permit' ? 'card-account-details' :
                                                                req === 'passport' ? 'passport' :
                                                                    req === 'hk_youth_plus' ? 'card-account-details' :
                                                                        req === 'address_proof' ? 'home' :
                                                                            req === 'student_id' ? 'school' :
                                                                                'card-account-details'}
                                                    size={24}
                                                    color={theme.colors.primary}
                                                />
                                            </View>
                                            <View style={styles.eventDetailContent}>
                                                <Text style={styles.eventDetailValue}>
                                                    {t(`events.detail.identityRequired.${req}`)}
                                                </Text>
                                            </View>
                                        </View>
                                    ))}
                                </View>
                            </View>
                        )}
                        {/* Add attachments section */}
                        {event.media_items && getAttachments(event.media_items).length > 0 && (
                            <View style={styles.detailSection}>
                                <Text style={styles.detailTitle}>{t('events.detail.attachments')}</Text>
                                <View style={styles.attachmentsContainer}>
                                    {getAttachments(event.media_items).map((item, index) => (
                                        <AttachmentItem
                                            key={`${item.file_path}-${index}`}
                                            attachment={item}
                                            theme={theme} // Use globalTheme
                                            styles={styles}
                                            t={t}
                                            openVideoModal={(url) => {
                                                setCurrentVideoUrl(url);
                                                updateVideoSource(url);
                                                setVideoModalVisible(true);
                                            }}
                                            router={router}
                                        />
                                    ))}
                                </View>
                            </View>
                        )}
                    </View>
                </ScrollView>

                {/* Enhanced Image Viewer */}
                <ImageViewer
                    visible={imageViewerVisible}
                    images={event?.media_items || []}
                    initialIndex={imageViewerInitialIndex}
                    onClose={closeImageViewer}
                    theme={theme}
                />

                {/* Video Player Modal (Placeholder) */}
                {currentVideoUrl && (
                    <Modal
                        visible={isVideoModalVisible}
                        transparent={true}
                        animationType="fade"
                        onRequestClose={() => {
                            player.pause();
                            setVideoModalVisible(false);
                            setCurrentVideoUrl(null);
                        }}
                    >
                        <TouchableOpacity
                            style={{
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                backgroundColor: 'rgba(0,0,0,0.7)'
                            }}
                            activeOpacity={1}
                            onPress={() => {
                                player.pause();
                                setVideoModalVisible(false);
                                setCurrentVideoUrl(null);
                            }}
                        >
                            <TouchableWithoutFeedback>
                                <View style={{
                                    backgroundColor: '#000',
                                    padding: 0,
                                    borderRadius: 0,
                                    width: '100%',
                                    height: SCREEN_HEIGHT * 0.4, // Adjust height as needed
                                }}>
                                    {isVideoModalVisible && (
                                        <VideoView
                                            player={player}
                                            contentFit="contain"
                                            nativeControls={true}
                                            style={{ width: '100%', height: '100%' }}
                                        />
                                    )}
                                </View>
                            </TouchableWithoutFeedback>
                        </TouchableOpacity>
                    </Modal>
                )}


                {/* 報名確認對話框：顯示報名須知，讓用戶確認是否報名 */}
                {/* All registration dialogs moved to EventActionButtons component */}

                {/* File error dialog - keep this as it's not related to event actions */}
                <TextDialog
                    visible={fileErrorDialogVisible}
                    title={t('common.error')}
                    message={fileErrorMessage}
                    confirmText={t('common.ok')}
                    onConfirm={() => setFileErrorDialogVisible(false)}
                    type="warning"
                />

                {/* All other event action dialogs moved to EventActionButtons component */}
            </View>
            {/* Event Action Buttons */}
            {event && (
                <EventActionButtons
                    event={event}
                    isAuthenticated={isAuthenticated}
                    currentUser={currentUser || undefined}
                    userEventRegistration={userEventRegistrationsData?.[0]}
                    eventVolunteerApplication={eventVolunteerApplication}
                    canVolunteerForEventOrg={canVolunteerForEventOrg()}
                    isLoadingOrgVolunteerStatus={isLoadingOrgVolunteerStatus}
                    registerAsParticipant={registerAsParticipantMutation}
                    applyAsVolunteer={applyAsVolunteerMutation}
                    cancelEventRegistration={cancelEventRegistrationMutation}
                    withdrawVolunteerApplication={withdrawVolunteerApplicationMutation}
                    isRegisteringAsParticipant={isRegisteringAsParticipant}
                    isApplyingAsVolunteer={isApplyingAsVolunteer}
                    isCancellingRegistration={isCancellingRegistration}
                    isWithdrawingVolunteerApp={isWithdrawingVolunteerApp}
                    refetchEventDetails={refetchEventDetails}
                    refetchUserEventRegistrations={refetchUserEventRegistrations}
                    refetchUserEventVolunteerApps={refetchUserEventVolunteerApps}
                    theme={theme}
                />
            )}
        </>
    );
}
