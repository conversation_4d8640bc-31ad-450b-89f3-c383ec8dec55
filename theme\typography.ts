import { Platform } from 'react-native';

export const typography = {
  // Font families
  fonts: {
    regular: Platform.select({
      ios: 'PingFangHK-Regular',
      android: undefined,
    }),
    medium: Platform.select({
      ios: 'PingFangHK-Medium',
      android: undefined,
    }),
    semibold: Platform.select({
      ios: 'PingFangHK-Semibold',
      android: undefined,
    }),
    bold: Platform.select({
      ios: 'PingFangHK-Bold',
      android: undefined,
    }),
  },

  // Font sizes
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
  },

  // Line heights
  lineHeights: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    xxl: 36,
  },
}; 