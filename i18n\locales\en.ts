import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { version } from 'react';

export default {
  common: {
    apply: 'Apply',
    cancel: 'Cancel',
    confirm: 'Confirm',
    back: 'Back',
    save: 'Save',
    continue: 'Continue',
    or: 'or',
    error: 'Error',
    ok: 'OK',
    home: 'Home',
    goBack: 'Go Back',
    imageLoadError: 'Failed to load image',
    step: 'Step',
    loading: 'Loading...',
    submit: 'Submit',
    photo: 'Photo',
    success: 'Success',
    askMeLater: 'Ask Me Later',
    dialog: {
      warning: 'Warning',
      info: 'Information',
      error: 'Error',
      success: 'Success',
    },
    viewAll: 'View All',
    locationUnavailable: 'Unknown Location',
    noDate: 'Unknown Date',
    noAuthor: 'Unknown Author',
    yes: 'Yes',
    no: 'No',
    eventOperationalStatus: {
      published: 'Published',
      archived: 'Archived',
      cancelled: 'Cancelled',
      draft: 'Draft',
      hidden: 'Hidden',
    },
    dynamicRoleTag: {
      participant: 'Participant',
      volunteer: 'Volunteer',
      pending: 'Pending',
    },
    edit: 'Edit',
    delete: 'Delete',
    create: 'Create',
    notification: 'Notification',
    searchPlaceholder: 'Search',
    noData: 'No data found',
    next: 'Next',
    complete: 'Complete',
    uploading: 'Uploading',
    uploaded: 'Uploaded',
    done: 'Done',
    close: 'Close',
    fileOpenError: 'Failed to open file',
    unknown: 'Unknown',
    thisOrganization: 'this organization',
    skipAndContinue: 'Skip & Continue',
    characters: 'characters',
  },
  documents: {
    hk_id_card: "Hong Kong ID Card",
    mainland_china_id_card: "Mainland China ID Card",
    mainland_travel_permit: "Home Return Permit/Mainland Travel Permit",
    address_proof: "Address Proof",
    student_id: "Student ID",
    home_visit: "Home Visit",
    passport: "Passport",
    hk_youth_plus: "HK Youth+"
  },
  error: {
    retry: 'Try Again',
    title: 'Something Went Wrong',
    message: 'Sorry, we couldn\'t find what you\'re looking for. Please try again later.',
    phoneVerification: {
      RATE_LIMIT_EXCEEDED: 'Too many attempts, please try again later',
      SYSTEM_ERROR: 'System error, please try again later',
      INVALID_CODE: 'Invalid verification code',
      CODE_EXPIRED: 'Verification code has expired',
      PHONE_EXISTS: 'Phone number already in use',
      INVALID_REQUEST: 'Invalid request',
      UNAUTHORIZED: 'Unauthorized',
      DISPLAY_NAME_REQUIRED: 'Display name is required',
      RESEND_FAILED_NO_STATE: 'Failed to resend code, please restart verification',
      SYSTEM_ERROR_RESEND: 'Failed to resend code, please try again later',
      ACCOUNT_LOCKED: 'Account temporarily locked due to too many failed attempts'
    },
    sendVerificationCode: {
      RATE_LIMIT_EXCEEDED: 'Too many attempts, please try again later',
      SYSTEM_ERROR: 'System error, please try again later',
      PHONE_REQUIRED: 'Phone number is required',
      INVALID_REQUEST: 'Invalid request, please try again',
      USER_NOT_FOUND: 'This phone number is not registered, a new account will be created',
      PHONE_CHECK_FAILED: 'Failed to verify phone number, please try again',
      LOGIN_INITIATE_FAILED: 'Failed to initiate login, please try again',
      REGISTRATION_INITIATE_FAILED: 'Failed to initiate registration, please try again',
      OTP_SEND_FAILED: 'Failed to send verification code, please check your phone number',
      SERVICE_UNAVAILABLE: 'Service temporarily unavailable, please try again later',
      INVALID_CHANNEL: 'Invalid verification method selected'
    },
    systemError: 'System error. Please try again later.',
    failed_to_load_registered_events: 'Failed to load registered events',
    failed_to_load_volunteer_applications: 'Failed to load volunteer applications',
    failed_to_load_events: 'Failed to load events',
    // HTTP error codes
    http: {
      401: 'Unauthorized access, please check your permissions',
      403: 'Access forbidden, insufficient permissions',
      404: 'Requested resource not found',
      500: 'Server error, please try again later',
      default: 'Network error, please check your connection and try again'
    },
  },
  navigation: {
    dashboard: 'Dashboard',
    explore: 'Explore',
    myEvents: 'My Events',
    profile: 'Profile',
    login: 'Login',
    settings: 'Settings',
    qrcode: 'QR Code',
  },
  auth: {
    appName: 'Membership App',
    slogan: 'Connect • Share • Inspire',
    logout: 'Logout',
    loginRequired: 'Login Required',
    loginRequiredMessage: 'Please login to access this feature',
    loginButton: 'Login',
    logoutConfirm: 'Are you sure you want to logout?',
    logoutError: 'Failed to logout. Please try again.',
    verifiedAccount: 'Verified Account',
    unverifiedAccount: 'Identity Unverified',
    underReviewAccount: 'Under Review',
    loginWithPhone: 'Login with Phone',
    signUp: 'Sign Up',
    login: 'Login',
    continueWithWhatsApp: 'Continue with WhatsApp',
    whatsAppVerificationTitle: 'WhatsApp Verification',
    whatsAppVerificationDesc: "We'll send you a verification code via WhatsApp to verify your number",
    registrationInfo: 'If this number is not registered, a new account will be created automatically.',
    registrationInfoWhatsApp: 'If this number is not registered, a new account will be created automatically. Make sure it is connected to WhatsApp.',
    termsAgreement: {
      prefix: 'By continuing, you agree to our',
      terms: 'Terms of Service',
      and: 'and',
      privacy: 'Privacy Policy'
    },
    termsOfService: 'Terms of Service',
    privacyPolicy: 'Privacy Policy',
    smsVerification: 'SMS Verification',
    smsVerificationDesc: "We'll send you a 6-digit code to verify your number",
    codeVerificationDesc: "Enter the 6-digit code sent to your phone",
    phonePrefix: '+852',
    phonePlaceholder: '1234 5678',
    phoneNumber: 'Phone Number',
    sendCodeError: 'Failed to send verification code. Please try again.',
    verificationError: 'Invalid verification code. Please try again.',
    resendCode: 'Resend code',
    resendCodeTimer: 'Resend code in {{seconds}}s',
    verify: 'Verify',
    modal: {
      title: 'Login Required',
      description: 'Log in to access more features and enjoy the full platform experience',
      loginButton: 'Log In Now',
      cancelButton: 'Cancel',
    },
    forgotAccount: 'Lost access to your account?',
    enterDisplayName: 'Enter Display Name',
    displayNamePlaceholder: 'e.g., John Doe',
    blockedTitle: 'Account Temporarily Locked',
    blockedMessage: 'Too many incorrect attempts. Please try again in {{minutes}} minutes.',
    blockedCountdown: 'Account locked. Try again in {{minutes}} minutes.',
  },
  identity: {
    title: 'Document Verification',
    subtitle: 'View and manage your document verification status.',
    documentVerification: {
      hkid: 'Hong Kong ID Card',
      mainland_id: 'Mainland ID Card',
      mainland_travel_permit: 'Home Return Permit or Mainland Travel Permit',
      passport: 'Passport',
      hkyouth: 'HK Youth+',
      address: 'Address Verification',
      student_card: 'Student Card',
    },
    verification: {
      title: 'Document Verification',
      subtitle: 'Please verify your documents to access all features',
      documentsSection: 'Document Verification',
      addressSection: 'Address Verification',
      requirementsTitle: 'Verification Requirements',
      statusLabels: {
        verified: 'Verified',
        pending: 'Under Review',
        rejected: 'Rejected',
        unverified: 'Unverified'
      },
      photo: {
        title: 'Upload Clear Photo',
        description: 'Take a clear photo in good lighting, ensuring all text is clearly visible.',
      },
    },
    info: {
      title: 'Personal Information',
      defaultTitle: 'Enter Information',
      englishName: 'English Name',
      title_hkid: 'Hong Kong ID Card Information',
      title_mainland_id: 'Mainland China ID Card Information',
      title_mainland_travel_permit: 'Home Return Permit / Mainland Travel Permit Information',
      title_passport: 'Passport Information',
      title_hk_youth: 'HK Youth+ Information',
      title_address: 'Address Proof Information',
      title_student_id: 'Student Card Information',
      softCopy: 'Upload using soft copy',
      hkidChineseName: 'Chinese Name (HKID)',
      hkidChineseNamePlaceholder: 'Optional, e.g., CHAN Tai Man',
      hkidEnglishName: 'English Name (HKID)',
      hkidEnglishNamePlaceholder: 'Must match ID, e.g., CHAN Tai Man',
      hkidNumber: 'Hong Kong ID Card Number',
      hkidPrefixPlaceholder: 'A111111',
      hkidCheckDigitPlaceholder: '9',
      hkidDob: 'Date of Birth',
      dobPlaceholder: 'DD-MM-YYYY',
      hkidGender: 'Gender',
      hkidPermanentResident: 'Permanent Hong Kong Resident?',
      hkidChineseCommercialCode: 'Chinese Commercial Code',
      hkidChineseCommercialCodePlaceholder: 'Optional, e.g., 1234',
      mainlandIdChineseName: 'Chinese Name (Mainland ID)',
      mainlandIdChineseNamePlaceholder: 'e.g., Li Xiao Ming',
      mainlandIdNumber: 'Mainland China ID Card Number',
      mainlandIdNumberPlaceholder: '18 digits, last may be X',
      mainlandIdDob: 'Date of Birth (Mainland ID)',
      mainlandIdGender: 'Gender (Mainland ID)',
      mainlandIdValidFrom: 'Valid From (Mainland ID)',
      mainlandIdExpiryDate: 'Expiry Date (Mainland ID)',
      expiryDatePlaceholder: 'DD-MM-YYYY',
      mainlandTravelPermitNumber: 'Home Return Permit/Travel Permit No.',
      mainlandTravelPermitNumberPlaceholder: 'e.g., H1234567890',
      mainlandTravelPermitIssueDate: 'Issue Date (Permit)',
      issueDatePlaceholder: 'DD-MM-YYYY',
      mainlandTravelPermitExpiryDate: 'Expiry Date (Permit)',
      passportNumber: 'Passport Number',
      passportNumberPlaceholder: 'e.g., K12345678',
      passportIssuingCountry: 'Issuing Country/Region (Passport)',
      selectCountry: 'Select Country',
      countryPlaceholder: 'Please select the issuing country/region',
      searchCountry: 'Search Country',
      passportIssueDate: 'Issue Date (Passport)',
      passportExpiryDate: 'Expiry Date (Passport)',
      hkYouthMembershipNumber: 'HK Youth+ Membership No.',
      hkYouthMembershipNumberPlaceholder: 'Enter your membership number',
      addressUnit: 'Unit/Flat',
      addressUnitPlaceholder: 'e.g., Flat A / Unit A',
      addressFloor: 'Floor',
      addressFloorPlaceholder: 'e.g., 10/F',
      addressBuilding: 'Building Name/Estate/Village / House No.',
      addressBuildingPlaceholder: 'e.g., Happy Mansion / Happy Garden Phase 1 / Happy Village / No. 10',
      addressStreet: 'Street Name & No.',
      addressStreetPlaceholder: 'e.g., 1 Queen\'s Road Central',
      addressDistrict: 'District',
      addressDistrictPlaceholder: 'e.g., Central and Western District / Yau Tsim Mong District',
      addressRegion: 'Region',
      addressRegionPlaceholder: 'Select Region',
      regionNewTerritories: 'New Territories',
      regionKowloon: 'Kowloon',
      regionHongKongIsland: 'Hong Kong Island',
      studentIdSchoolName: 'School Name',
      studentIdSchoolNamePlaceholder: 'e.g., XX Secondary School / XX University',
      studentIdGrade: 'Grade/Program',
      studentIdGradePlaceholder: 'e.g., Form 4 / Year 1',
      studentIdExpiryDate: 'Student ID Expiry Date',
      male: 'Male',
      female: 'Female',
      next: 'Next',
      validation: {
        chineseNameRequired: 'Chinese name is required',
        englishNameRequired: 'English name is required',
        idCardNumberRequired: 'ID number is required',
        idCardNumberInvalid: 'Invalid Hong Kong ID card format',
        idCardNumberInvalidChecksum: 'Invalid HKID card number. Please check and try again.',
        mainlandTravelPermitInvalid: 'Invalid Home Return Permit/Travel Permit format',
        mainlandIdInvalid: 'Invalid Mainland China ID card format',
        passportInvalid: 'Invalid passport number format',
        countryRequired: 'Country/Region is required',
        dateOfBirthRequired: 'Date of birth is required',
        dateOfBirthInvalid: 'Invalid date format, please use DD-MM-YYYY',
        dateOfBirthFuture: 'Date of birth cannot be in the future',
        expiryDateRequired: 'Expiry date is required',
        expiryDateInvalid: 'Invalid expiry date format, please use DD-MM-YYYY',
        expiryDatePast: 'Expiry date cannot be in the past',
        issueDateRequired: 'Issue date is required',
        issueDateInvalid: 'Invalid issue date format, please use DD-MM-YYYY',
        issueDateFuture: 'Issue date cannot be in the future',
        regionRequired: 'Region is required',
        genericRequired: '{{fieldName}} is required',
        termsRequired: 'Please agree to the terms and conditions',
        hkidInvalid: 'Invalid Hong Kong ID card format.',
      },
    },
    guide: {
      title: 'Identity Verification Guide',
      subtitle: 'Follow these steps to quickly and securely complete identity verification.',
      selectDocument: 'Select Document Type',
      stepsTitle: 'Verification Steps',
      steps: {
        info: {
          title: 'Enter Personal Information',
          description: 'Before uploading document photos, please fill in and verify your personal details.',
          title_hk_id_card: 'Verify Hong Kong ID Information',
          description_hk_id_card: 'Please have your Hong Kong ID card ready and confirm the following details match your ID.',
          title_mainland_china_id_card: 'Verify Mainland China ID Information',
          description_mainland_china_id_card: 'Please have your Mainland China ID card ready and confirm the details match your ID.',
          title_mainland_travel_permit: 'Verify Permit Information',
          description_mainland_travel_permit: 'Please have your Home Return Permit/Mainland Travel Permit ready and confirm the details.',
          title_passport: 'Verify Passport Information',
          description_passport: 'Please have your passport ready and confirm the details match your document.',
          title_hk_youth_plus: 'Verify HK Youth+ Information',
          description_hk_youth_plus: 'Please have your HK Youth+ membership details ready.',
          title_address_proof: 'Enter Address Information',
          description_address_proof: 'Please have your address proof document ready and enter the address exactly as it appears.',
          title_student_id: 'Verify Student ID Information',
          description_student_id: 'Please have your student ID ready and confirm the details match your card.',
          title_home_visit: 'Home Visit Application Information',
          description_home_visit: 'Please confirm your details for the home visit application.',
          title_volunteer: 'Volunteer Application Information',
          description_volunteer: 'Please confirm your details for the volunteer application.',
        },
        prepare: {
          title: 'Prepare Your Document',
          description: 'Have your original document ready. Ensure it is clean and undamaged.',
          title_hk_id_card: 'Prepare Original Hong Kong ID Card',
          description_hk_id_card: 'Have your original Hong Kong ID card. Ensure it is clean, undamaged, and all details are clear.',
          title_mainland_china_id_card: 'Prepare Original Mainland China ID Card',
          description_mainland_china_id_card: 'Have your original Mainland China ID card. Ensure it is clean, undamaged, and details are clear.',
          title_mainland_travel_permit: 'Prepare Original Permit',
          description_mainland_travel_permit: 'Have your original Home Return Permit/Mainland Travel Permit. Ensure it is clean and clear.',
          title_passport: 'Prepare Original Passport',
          description_passport: 'Have your original passport. Ensure the information page is clear.',
          title_hk_youth_plus: 'Prepare HK Youth+ Proof',
          description_hk_youth_plus: 'If applicable, prepare your HK Youth+ membership card or relevant proof.',
          title_address_proof: 'Prepare Original Address Proof',
          description_address_proof: 'Have an original address proof document from the last 3 months (e.g., utility bill, bank statement).',
          title_student_id: 'Prepare Original Student ID',
          description_student_id: 'Have your original student ID. Ensure all details are clear.',
          title_home_visit: 'Prepare for Home Visit Application',
          description_home_visit: 'Ensure all necessary information is ready.',
          title_volunteer: 'Prepare for Volunteer Application',
          description_volunteer: 'Ensure all necessary information is ready for volunteering.',
        },
        photo: {
          title: 'Upload Clear Photos',
          description: 'Take clear photos in good lighting. Ensure all text is legible.',
          title_hk_id_card: 'Photograph Hong Kong ID Card',
          description_hk_id_card: 'Photograph the front and back of your HKID. Ensure photos are clear, complete, and without glare.',
          title_mainland_china_id_card: 'Photograph Mainland China ID Card',
          description_mainland_china_id_card: 'Photograph the front and back of your ID. Ensure photos are clear, complete, and without glare.',
          title_mainland_travel_permit: 'Photograph Permit',
          description_mainland_travel_permit: 'Photograph the personal information page of your permit. Ensure it is clear and complete.',
          title_passport: 'Photograph Passport',
          description_passport: 'Photograph the personal information page of your passport. Ensure it is clear and complete.',
          title_hk_youth_plus: 'Upload HK Youth+ Proof Photo',
          description_hk_youth_plus: 'Photograph your HK Youth+ card or proof. Ensure it is clear.',
          title_address_proof: 'Photograph Address Proof',
          description_address_proof: 'Photograph the full page of your address proof. Ensure name, address, and issue date are clear.',
          title_student_id: 'Photograph Student ID',
          description_student_id: 'Photograph the front of your student ID. If there is information on the back, photograph it too.',
          title_home_visit: 'Upload Home Visit Documents',
          description_home_visit: 'Upload any supporting documents if required for the home visit program.',
          title_volunteer: 'Upload Volunteer Documents',
          description_volunteer: 'Upload any supporting documents if required for the volunteer program.',
        },
        review: {
          title: 'Await Review Outcome',
          description: 'After submission, we will complete verification within 3-5 working days. You will be notified upon completion.',
          title_hk_id_card: 'Await HKID Review',
          description_hk_id_card: 'We will review your HKID information shortly, typically within 3-5 working days.',
          title_mainland_china_id_card: 'Await Mainland ID Review',
          description_mainland_china_id_card: 'We will review your Mainland ID information shortly, typically within 3-5 working days.',
          title_mainland_travel_permit: 'Await Permit Review',
          description_mainland_travel_permit: 'We will review your permit information shortly, typically within 3-5 working days.',
          title_passport: 'Await Passport Review',
          description_passport: 'We will review your passport information shortly, typically within 3-5 working days.',
          title_hk_youth_plus: 'Await HK Youth+ Review',
          description_hk_youth_plus: 'We will review your HK Youth+ information shortly.',
          title_address_proof: 'Await Address Proof Review',
          description_address_proof: 'We will review your address proof shortly, typically within 3-5 working days.',
          title_student_id: 'Await Student ID Review',
          description_student_id: 'We will review your student ID information shortly, typically within 3-5 working days.',
          title_home_visit: 'Await Home Visit Application Review',
          description_home_visit: 'Your home visit application will be reviewed by our team.',
          title_volunteer: 'Await Volunteer Application Review',
          description_volunteer: 'Your volunteer application will be reviewed by our team.',
        }
      },
      startButton: 'Start Verification',
    },
    steps: {
      button: 'Submit Document Verification',
      front: {
        title: 'Front Photo',
        description: 'Please upload a clear photo of the front of your document. Ensure all text is clearly visible.',
        hk_id_card: {
          title: 'Please upload the front of your Hong Kong ID Card',
          sample: 'Example: Hong Kong ID Card Front',
        },
        mainland_china_id_card: {
          title: 'Please upload the front of your Mainland ID Card',
          sample: 'Example: Mainland ID Card Front',
        },
        mainland_travel_permit: {
          title: 'Please upload the front of your Home Return Permit/Mainland Travel Permit',
          sample: 'Example: Home Return Permit/Mainland Travel Permit Front',
        },
        passport: {
          title: 'Please upload your Passport photo',
          sample: 'Example: Passport Information Page',
        },
        hk_youth_plus: {
          title: 'Upload HK Youth+ Photo',
          sample: 'Example: HK Youth+ Photo',
        },
        address_proof: {
          title: 'Please upload Address Proof Photo',
          sample: 'Example: Address Proof',
        },
        student_id: {
          title: 'Please upload the front of your Student Card',
          sample: 'Example: Student Card Front',
        },
        default: {
          title: 'Please upload the front of your document',
          sample: 'Example: Document Front',
        },
        error: {
          noImage: 'Please upload front photo',
          missingImage: 'Front photo required',
        },
        success: {
          title: 'Front photo uploaded',
          message: 'Front photo successfully uploaded, please continue with the back photo.',
        },
      },
      back: {
        title: 'Back Photo',
        description: 'Please upload a clear photo of the back of your document. Ensure all text is clearly visible.',
        hk_id_card: {
          title: 'Please upload the back of your Hong Kong ID Card',
          sample: 'Example: Hong Kong ID Card Back',
        },
        mainland_china_id_card: {
          title: 'Please upload the back of your Mainland ID Card',
          sample: 'Example: Mainland ID Card Back',
        },
        mainland_travel_permit: {
          title: 'Please upload the back of your Home Return Permit/Mainland Travel Permit',
          sample: 'Example: Home Return Permit/Mainland Travel Permit Back',
        },
        student_id: {
          title: 'Please upload the back of your Student Card',
          sample: 'Example: Student Card Back',
        },
        default: {
          title: 'Please upload the back of your document',
          sample: 'Example: Document Back',
        },
        error: {
          missingImage: 'Back photo required',
        },
        success: {
          title: 'Success',
          message: 'Your document verification has been submitted',
          confirm: 'Confirm',
        }
      },
    },
    upload: {
      title: 'Choose Upload Method',
      camera: 'Take Photo',
      gallery: 'Choose from Gallery',
      button: 'Upload Photo',
    },
    permissions: {
      gallery: 'Please allow access to gallery to upload photos.',
      camera: 'Please allow camera access to take photos.',
    },
    errors: {
      upload: 'Failed to upload photo, please try again.',
      incomplete: 'Please upload both front and back photos of your ID before submitting.',
      incompletePassport: 'Please upload a photo of your passport information page.',
      capture: 'Failed to capture photo, please try again.',
      invalidImage: 'Photo does not meet requirements, please upload again.',
      fileTooLarge: 'Image size cannot exceed 10MB',
      noDocType: 'Document type not specified. Please go back and try again.',
      submissionFailed: 'Submission failed. Please try again later.',
      noFieldsConfigured: 'No information fields are configured for document type: {{docType}}. Please check the configuration.',
    },
    success: {
      title: 'Success',
      message: 'Your identity verification has been submitted',
      confirm: 'Confirm',
      homeVisit: 'Home Visit Program application successfully submitted!',
      volunteer: 'Volunteer Program application successfully submitted!',
    },
    terms: {
      default: {
        title: "Terms and Conditions",
        content: "Please read and agree to the relevant terms and conditions.",
      },
      home_visit: {
        title: "Home Visit Program Terms and Conditions",
        content: "Specific terms for the Home Visit Program go here...",
      },
      volunteer: {
        title: "Volunteer Program Terms and Conditions",
        content: "Specific terms for the Volunteer Program go here...",
      },
      agreeToTerms: "I have read and agree to the above terms and conditions",
    }
  },
  profile: {
    title: 'Profile',
    sections: {
      accountSettings: 'Account Settings',
      security: 'Security',
      preferences: 'Preferences',
      supportAndAbout: 'Support & About',
      verification: 'Verification & Applications',
    },
    organization: {
      tapToChange: 'Tap to change organization',
      tapToManage: 'Tap to manage your organizations',
      manageOrganizations: 'Manage Organizations',
      selectToJoin: 'Select organizations you want to join or leave',
      alreadyJoined: 'Already joined',
      updateSettings: 'Update Organization Settings',
      updateSuccess: {
        title: 'Settings Updated',
        message: 'Your organization settings have been updated successfully.'
      },
      updateError: {
        title: 'Update Failed',
        message: 'Failed to update organization settings. Please try again.'
      },
      noOrganizations: {
        title: 'No Organizations Available',
        message: 'There are currently no organizations available to join.'
      },
    },
    errors: {
      settingsLoadFailed: 'Failed to load user settings, please try again later',
    },
    avatar: {
      title: 'Change Avatar',
      success: 'Avatar updated successfully',
      error: 'Failed to update avatar',
      errors: {
        FILE_TOO_LARGE: 'Image size cannot exceed 5MB',
        INVALID_FORMAT: 'Invalid image format',
        SYSTEM_ERROR: 'System error, please try again later'
      }
    },
    guest: 'Guest',
    logoutErrorDesc: 'Unable to logout, please try again later',
    logoutConfirmTitle: 'Logout Confirmation',
    logoutConfirmMessage: 'Are you sure you want to logout?',
    identityVerification: 'Identity Verification',
    applicationStatus: 'Program Applications',
    latestApplication: 'Latest Application',
    editProfile: 'Identity Information',
    notifications: 'Notifications',
    myStats: 'My Stats',
    helpCenter: 'Help Center',
    terms: 'Terms of Service',
    privacy: 'Privacy Policy',
    editUsernameTitle: 'Edit Username',
    usernameLabel: 'Username',
    usernameRequired: 'Username is required',
    usernameUpdateError: 'Failed to update username',
    language: 'Language Settings',
    logout: 'Logout',
    deleteAccount: {
      title: 'Delete Account',
      whatWillBeDeleted: 'What Will Be Deleted',
      step1: {
        title: 'Personal Information',
        description: 'Your name, phone number, email, and all identity verification documents will be permanently deleted.',
      },
      step2: {
        title: 'Event History',
        description: 'All your event registrations, participation history, and volunteer applications will be removed.',
      },
      step3: {
        title: 'Account Data',
        description: 'Your login credentials, preferences, and all associated account data will be permanently deleted.',
      },
      warning: {
        title: 'This Action Cannot Be Undone',
        description: 'Once you delete your account, you will not be able to recover your data. Please make sure you have downloaded any important information before proceeding.',
      },
      continue: 'Confirm',
      confirmModal: {
        title: 'Delete Account',
        message: 'This action cannot be undone. Are you sure you want to permanently delete your account?',
        confirm: 'Delete Account',
        deleting: 'Deleting...',
      },
      success: {
        title: 'Account Deleted',
        message: 'Your account has been successfully deleted.',
      },
      error: {
        title: 'Delete Failed',
        message: 'Failed to delete account. Please check your network connection and try again.',
      },
    },
    appStatus: {
      noApplications: 'No applications submitted',
      volunteerPending: 'Volunteer application under review',
      volunteerApproved: 'Volunteer application approved',
      someRejected: 'Application(s) rejected',
      summary: {
        someRejected: 'Some were rejected',
        somePending: 'Some under review',
        allApproved: 'All approved',
        someApproved: 'Some approved',
        allUnverifiedOrNotSubmitted: 'No submitted',
      }
    },
    idStatus: {
      allVerified: 'All documents verified',
      somePending: 'Verification in progress',
      someRejected: 'Verification rejected',
      hkidVerifiedOnly: 'ID verified, address pending',
      addressVerifiedOnly: 'Address verified, ID pending',
      noneVerified: 'No documents verified'
    },
    username: {
      edit: 'Edit',
      title: 'Edit Username',
      placeholder: 'Enter username',
      save: 'Save',
      success: 'Username updated successfully',
      error: 'Failed to update username',
      editRules: 'Username must be 4-12 characters long and cannot be changed again within 15 days after modification',
      errors: {
        INVALID_USERNAME: 'Invalid username format',
        SYSTEM_ERROR: 'System error, please try again later'
      }
    },
    changePhone: {
      title: 'Change Phone Number',
      smsVerification: 'Phone Number Verification',
      smsVerificationDesc: 'Enter your new phone number',
      codeVerificationDesc: 'Enter the 6-digit code sent to your new phone number',
      success: {
        title: 'Phone Number Changed',
        message: 'Your phone number has been successfully updated.',
      },
      verifyCurrentPhone: 'Verify Current Phone Number',
      verifyCurrentPhoneDesc: 'Please select a verification method to continue changing your phone number',
      verificationInfo: 'We will send a verification code to your phone',
      selectMethod: 'Select Verification Method',
      useWhatsApp: 'Verify with WhatsApp',
      useSMS: 'Verify with SMS',
      smsDesc: 'We will send a verification code to your phone',
      whatsappDesc: 'We will send a verification code via WhatsApp',
      verificationCodeDesc: 'Enter the 6-digit code sent to your phone',
      verifyNewPhoneCode: 'Verify New Phone Number',
      verifyNewPhoneCodeDesc: 'Enter the 6-digit code sent to your new phone number',
    },
    changePhoneGuide: {
      header: 'Change Phone Number Guide',
      title: 'Change Phone Number',
      important: 'Important Information',
      description: 'Changing your phone number will affect your account login information. After changing, you will need to use your new phone number for login.',
      howItWorks: 'How It Works',
      step1: {
        title: 'Verify Old Number',
        description: 'You will need to verify your old phone number via SMS verification code.'
      },
      step2: {
        title: 'Verify New Number & Account Transfer',
        description: 'Once verified, your account will be securely transferred to your new phone number. You will use this number for all future logins and account recovery.'
      },
      trouble: {
        title: 'Can\'t access your current phone number?',
        description: 'If you no longer have access to your current phone number, please send an email to us.'
      },
      continue: 'Continue to Change Phone',
      needHelp: 'I Need Help With My Account'
    },
    edit: {
      title: 'Identity Information',
      notice: {
        title: 'Important Notice',
        message: 'This information will be used for identity verification.\nOnce verified, it cannot be modified without contacting customer service.',
      },
      save: 'Save',
      success: {
        title: 'Identity Information Updated',
        message: 'Your identity information has been updated successfully.',
      },
      error: {
        title: 'Update Failed',
        message: 'Failed to update identity information. Please try again.',
      },
    },
    stats: {
      title: 'My Stats',
      joinedDays: 'Days with us',
      overview: {
        totalEvents: 'Total Events',
        totalHours: 'Total Hours',
        volunteerEvents: 'Volunteer Events',
      },
      chart: {
        title: 'Activity Statistics',
        hint: 'Last 6 months only',
        noData: 'No data available',
        hours: 'Hours',
        events: 'Events',
      },
      details: {
        monthly: {
          title: 'Monthly Activity',
          sixMonthsNote: 'Last 6 months only',
        },
        categories: {
          title: 'Activity Types',
          viewAll: 'View All Events',
          noData: 'No past events',
        },
      },
    },
    items: {
      editProfile: {
        title: 'Identity Information',
        description: 'Manage identity verification details',
      },
      phoneNumber: {
        title: 'Change Phone Number',
        description: 'Update your phone number',
      },
      identity: {
        title: 'ID Card Verification',
        unverified: 'Not verified, please verify',
        pending: 'Under review',
        verified: 'Verified',
        rejected: 'Verification rejected',
      },
      address: {
        title: 'Address Verification',
        unverified: 'Not verified',
        pending: 'Under review',
        verified: 'Verified',
        rejected: 'Verification rejected',
        requiresIdCard: {
          title: 'Verification Required',
          message: 'ID card verification must be completed before proceeding with address verification.',
          button: 'Verify Now'
        }
      },
      email: {
        title: 'Email Address',
        description: 'Add email for notifications',
      },
      twoFactor: {
        title: 'Two-Factor Authentication',
        description: 'Add extra security to your account',
      },
      loginDevices: {
        title: 'Login Devices',
        description: 'Manage your connected devices',
      },
      notifications: {
        title: 'Notifications',
        description: 'Manage push notifications',
      },
      language: {
        title: 'Language',
        description: 'English',
        choose: 'Choose Language',
      },
      theme: {
        title: 'Theme',
        description: {
          light: 'Light mode',
          dark: 'Dark mode',
        },
        choose: 'Choose Theme',
        options: {
          light: 'Light Mode',
          dark: 'Dark Mode',
        },
      },
      myStats: {
        title: 'My Stats',
        description: 'View my activity statistics',
      },
      helpCenter: {
        title: 'Help Center',
        description: 'Get support and FAQ',
      },
      privacyPolicy: {
        title: 'Privacy Policy',
      },
      termsOfService: {
        title: 'Terms of Service',
      },

    },
    accountRecovery: {
      title: 'Account Recovery',
      description: 'If you have lost access to your account, please fill in the form below. Our customer service team will review your request and contact you within 1-2 business days.',
      phoneNumber: 'Phone Number',
      email: 'Contact Email',
      reason: 'Reason for Recovery',
      submit: 'Submit Request',
      emailError: 'Please enter a valid email address',
      documentsTitle: 'Identity Documents',
      documentsDescription: 'Please upload up to 2 images of documents that can help verify your identity (e.g. ID card, passport).',
      addDocumentImage: 'Add Document',
      uploadOptions: {
        title: 'Upload Document',
        camera: 'Take Photo',
        gallery: 'Choose from Gallery'
      },
      errors: {
        fileTooLarge: 'Image size cannot exceed 10MB',
        invalidImage: 'Invalid image format',
        capture: 'Failed to capture photo. Please try again.',
        upload: 'Failed to upload image. Please try again.'
      },
      success: {
        title: 'Request Submitted',
        message: 'Your account recovery request has been submitted successfully. We will contact you via email within 1-2 business days.',
      },
      error: {
        title: 'Submission Failed',
        message: 'Failed to submit your request. Please try again later or contact our customer service.',
      },
    },
    version: 'Version {{version}}',
  },
  dashboard: {
    title: 'Dashboard',
    welcome: {
      subtitle: 'Explore events with the community',
      greeting: 'Welcome back',
    },
    commonFeatures: {
      title: 'Common Features',
      searchEvents: 'Search Events',
      myStats: 'My Stats',
      eventHistory: 'Event History',
      helpCenter: 'Help Center',
    },
    posts: {
      title: 'Latest Posts',
      empty: 'No latest posts',
    },
    upcomingEvents: {
      title: 'Upcoming Events',
      empty: 'No upcoming events',
    },
    selectOrganization: 'Select Organization',
    switchOrganization: 'Switch Organization',
    allOrganizations: 'All Organizations',
  },
  posts: {
    detail: {
      title: 'Posts Details',
      notFound: 'Posts not found',
      lastUpdated: 'Last Updated:',
      postsNotFoundDesc: 'Sorry, the posts article you are looking for does not exist or has been removed.',
      attachments: 'Attachments',
      preview: 'Preview',
      download: 'Download',
      previewError: 'Unable to preview file. Please try again later.',
      downloadError: 'Unable to download file. Please try again later.',
    },
    share: {
      title: 'Posts Details',
      author: 'Author',
      readMore: 'Read More',
      appNotInstalled: 'Get our app',
      appStoreLink: 'https://apps.apple.com/app/your-app-id',
      playStoreLink: 'https://play.google.com/store/apps/details?id=your.app.id',
    }
  },
  explore: {
    title: 'Explore',
    searchPlaceholder: {
      events: 'Search events...',
      posts: 'Search posts...',
      resources: 'Search resources...',
    },
    tabs: {
      events: 'Events',
      posts: 'Posts',
      resources: 'Resources',
    },
    categories: {
      all: 'All',
      business: 'Business',
      technology: 'Technology',
      design: 'Design',
      marketing: 'Marketing',
    },
    free: 'FREE',
    attendees: '{{count}} attendees',
    loadingMore: 'Loading more...',
    filters: {
      title: 'Filters',
      reset: 'Reset',
      allEventTypes: 'All Event Types',
      allPostTypes: 'All Post Types',
      eventTypes: 'Event Types',
      postTypes: 'Post Types',
      date: {
        title: 'Date',
        all: 'All Dates',
        today: 'Today',
        tomorrow: 'Tomorrow',
        next7Days: 'Next 7 Days',
        next30Days: 'Next 30 Days',
        latest: 'Latest',
        last7Days: 'Last 7 Days',
        last30Days: 'Last 30 Days',
      },
      price: {
        title: 'Price',
        all: 'All prices',
        free: 'Free',
        paid: 'Paid',
        under100: 'Under HK$100',
        under500: 'Under HK$500',
      },
      organization: 'Organization',
    },
    noEventsTitle: 'No Events Found',
    noEventsText: 'Try adjusting your filters or search terms to find more events.',
    noPostsTitle: 'No Posts Found',
    noPostsText: 'Try adjusting your filters or search terms to find more posts.',
    noResourcesTitle: 'No Resources Found',
    noResourcesText: 'Try adjusting your filters or search terms to find more resources.',
  },
  events: {
    detail: {
      title: 'Event Details',
      notFound: 'Event not found',
      noDescription: 'No description available',
      eventNotFoundDesc: 'Sorry, the event you are looking for does not exist or has been removed.',
      description: 'About this event',
      participate: 'Participate',
      volunteer: 'Volunteer',
      eventFull: 'Event Full',
      joinWaitingList: 'Join Waiting List',
      free: 'Free',
      governmentFunding: 'Government Funding',
      identityRequired: {
        hk_id_card: 'Hong Kong ID Card required',
        passport: 'Passport required',
        mainland_travel_permit: 'Home Return Permit/Mainland Travel Permit required',
        hk_youth_plus: 'HKYouth+ required',
        address_proof: 'Address Proof required',
        student_id: 'Student ID required',
        home_visit: 'Home Visit required',
        mainland_china_id_card: 'Mainland China ID Card required',
      },
      share: 'Share',
      save: 'Save',
      saved: 'Saved',
      organizer: 'Organizer',
      attendees: 'Registered',
      duration: 'Duration',
      location: 'Location',
      viewDetails: 'Event Details',
      dateAndTime: 'Date & Time',
      startDateTime: 'Start Time',
      endDateTime: 'End Time',
      price: 'Price',
      maxWaitingList: 'Max Waiting List',
      people: 'people',
      idRequired: 'ID Verification',
      video: 'Video',
      attachments: 'Attachments',
      watchVideo: 'Watch Video',
      thingsToKnow: 'Things to Know',
      whatToExpect: 'What to expect',
      eventEnded: 'Event Ended',
      openMap: 'Open Map',
      chooseMapApp: 'Choose Map App',
      openToMapApp: 'Click to open Map App',
      addToCalendar: 'Click to add to Calendar',
      addToCalendarDesc: 'Would you like to add this event to your calendar?',
      addToCalendarConfirm: 'Add Event',
      addToCalendarSuccess: 'Added to Calendar',
      addToCalendarSuccessDesc: 'Event has been successfully added to your calendar',
      addToCalendarError: 'Failed to Add to Calendar',
      addToCalendarErrorDesc: 'Unable to add event to calendar. Please try again later',
      icsDownloaded: 'ICS file downloaded',
      shareIcsDialogTitle: 'Share Calendar Event',
      shareActionTitle: 'Share Initiated',
      shareActionMessage: 'Your event is ready to be shared',
      calendarPermissionTitle: 'Calendar Permission',
      calendarPermissionMessage: 'This app needs calendar permission to add events',
      calendarPermissionDenied: 'Calendar Permission Denied',
      calendarPermissionDeniedMessage: 'Calendar permissions are required to add events to your calendar. Please enable them in your device settings.',
      cancelParticipation: 'Cancel Participation',
      cancelParticipationDesc: 'Are you sure you want to cancel your participation in this event?',
      cancelParticipationConfirm: 'Confirm Cancellation',
      calendarReadOnly: Constants.platform?.ios ?
        'Cannot write to calendar. Please check if iCloud Calendar is enabled or choose another calendar' :
        'Cannot write to calendar. Please ensure Google Calendar is set as the default calendar',
      permissionRequired: 'Calendar Permission Required',
      permissionDeniedIOS: 'Please go to Settings > Privacy & Security > Calendar to allow app access to calendar',
      permissionDeniedAndroid: 'Please go to Settings > Apps > Permissions to allow app access to calendar',
      openSettings: 'Open Settings',
      invalidDateFormat: 'Invalid date format, please contact customer service',
      eventAlreadyAdded: 'Event Already Added',
      eventAlreadyAddedDesc: 'This event is already in your calendar',
      verificationRequired: 'Verification Required',
      verification: {
        pending: {
          title: 'Verification Pending',
          message: 'Your verification is currently under review. We will notify you once it is complete.',
        }
      },
      verificationGuide: 'To ensure the safety and quality of our events, more verifications are required:\n\n{{items}}\n\nWould you like to complete the verification now?',
      goToVerification: 'Complete Verification',
      alreadyRegistered: 'Already Registered',
      alreadyRegisteredMessage: 'You have already registered for this event.',
      registrationSuccess: 'Registration Successful',
      participantSuccessGuide: 'You have successfully registered for this event. You can find the event details in "My Events". Remember to:\n\n• Save the event date\n• Check the requirements\n• Arrive on time with your QR code',
      volunteerSuccessGuide: 'Your volunteer application has been submitted. Please note:\n\n• Staff will review your application\n• You will receive a notification once approved\n Check "My Events" for application status',
      confirmRegistration: 'Confirm Registration',
      participantConfirmGuide: 'You are about to register for this event as a participant. Please make sure you have read and understood all the event requirements.',
      volunteerConfirmGuide: 'You are about to register as a volunteer for this event. Please note that your application will need to be reviewed by our staff, which typically takes 1-2 business days. Please make sure you have read and understood all the volunteer requirements.',
      registrationFailed: 'Registration Failed',
      registrationFailedParticipant: 'You have already registered for this event as a participant.',
      registrationFailedVolunteer: 'You have already registered for this event as a volunteer.',
      registrationFailedUnknown: 'Registration failed. Please try again later.',
      duplicateRegistration: 'You have already registered for this event.',
      duplicateVolunteerApplication: 'You have already applied as a volunteer for this event.',
      cancelSuccess: 'Cancelled Successfully',
      cancelSuccessMessage: 'Your registration has been cancelled',
      volunteerOpportunity: 'Volunteer Opportunity',
      missingVerificationsError: 'Required verifications: {{verifications}}',
      pendingVerificationsWarning: 'Pending verifications: {{verifications}}',
      verificationRequiredTitle: 'Verification Required',
      waitlistedSuccess: 'Added to waitlist successfully',
      volunteerApplicationSuccess: 'Volunteer application submitted',
      volunteerApplicationFailed: 'Failed to submit volunteer application',
      volunteerWithdrawSuccess: 'Volunteer application withdrawn',
      withdrawVolunteerSuccess: 'Volunteer application withdrawn successfully',
      registrationError: 'Registration failed',
      cancelError: 'Cancellation failed',
      volunteerApplicationError: 'Volunteer application failed',
      volunteerJoinOrgTitle: 'Organization Volunteer Required',
      volunteerJoinOrgMessage: 'Be a volunteer at {{organizationName}} to apply for this volunteer position',
      loadingVolunteerStatus: 'Loading volunteer status...',
      joinParticipant: 'Join Event',
      cancelWaitlistSpot: 'Cancel Waitlist',
      statusPendingApproval: 'Pending Approval',
      statusAttended: 'Attended',
      registrationClosed: 'Registration Closed',
      joinWaitlist: 'Join Waitlist',
      cancelVolunteerRole: 'Cancel Volunteer Role',
      volunteerApplicationPending: 'Volunteer Application Pending',
      volunteerConfirmed: 'Volunteer Confirmed',
      applicationsClosed: 'Applications Closed',
      eventCancelled: 'Event Cancelled',
      cancelConfirmTitle: 'Confirm Cancellation',
      cancelParticipantConfirmMessage: 'Are you sure you want to cancel your participation?',
      cancelVolunteerConfirmMessage: 'Are you sure you want to cancel your volunteer application?',
      cancelRegistrationTitle: 'Cancel Registration',
      withdrawVolunteerTitle: 'Withdraw Volunteer Application',
      cancelRegistrationMessage: 'Are you sure you want to cancel your registration for this event?',
      withdrawVolunteerMessage: 'Are you sure you want to withdraw your volunteer application for this event?',
      withdrawVolunteerError: 'Failed to withdraw volunteer application',
      imageDownload: {
        downloading: 'Downloading...',
        permissionRequired: 'Permission Required',
        permissionMessage: 'Please grant permission to save photos to your library.',
        success: 'Success',
        successMessage: 'Image saved to your photo library!',
        error: 'Error',
        errorMessage: 'Failed to save image. Please try again.',
        imageNotFound: 'Image not found.',
      },
      cancelReasonTitle: 'Cancellation Reason',
      cancelReasonPlaceholder: 'Please provide a reason for cancellation (optional)',
      loadingStatus: 'Loading status...',
      eventNotLoaded: 'Event details not loaded, please try again.',
      appleMapApp: 'Apple Map',
      googleMapApp: 'Google Map',
    },
    share: {
      title: 'Event Details',
      joinUs: 'Join us at this event!',
      type: 'Event Type',
      participants: 'Participants',
      role: 'Your Role',
      status: 'Status',
      openInApp: 'Open in App',
      appNotInstalled: 'Get our app',
      appStoreLink: 'https://apps.apple.com/app/your-app-id',
      playStoreLink: 'https://play.google.com/store/apps/details?id=your.app.id',
      free: 'Free',
      viewOnMap: 'View on Google Maps',
      openInWebapp: 'Open in Web App',
    },
  },
  my_events: {
    title: 'My Events',
    filters: {
      all: 'All Dates',
      participant: 'Normal',
      volunteer: 'Volunteer',
      today: 'Today',
      tomorrow: 'Tomorrow',
      next7days: 'Next 7 days',
      next30days: 'Next 30 days',
      past: 'Past Events',
      all_types: 'All Types',
      all_past: 'All Past Events',
      select_date_range: 'Select Date Range',
      select_event_type: 'Select Event Type',
      date: {
        all: 'All Dates',
        today: 'Today',
        tomorrow: 'Tomorrow',
        next7Days: 'Next 7 days',
        next30Days: 'Next 30 days',
      },
    },
    roleTag: {
      participant: 'Participant',
      volunteer: 'Volunteer',
    },
    role_registered: 'Participant',
    role_volunteering: 'Volunteer',
    status: {
      participant: 'Participant',
      volunteer: 'Volunteer',
      upcoming: 'Upcoming',
      completed: 'Completed',
      cancelled: 'Cancelled',
      notAttended: 'Not Attended',
      unknown: 'Unknown Status',
      registered: 'Registered',
      waitlisted: 'Waitlisted',
      pending_approval: 'Pending Approval',
      pending: 'Pending',
      approved: 'Approved',
      attended: 'Attended',
      rejected: 'Rejected',
      absent: 'Absent',
      volunteerPending: 'Pending',
      volunteerApproved: 'Approved',
      volunteerRejected: 'Rejected',
      cancelled_by_user: 'Cancelled by You',
      cancelled_by_admin: 'Cancelled by Organizer',
      rejected_approval: 'Approval Rejected',
      pending_payment: 'Pending Payment',
      confirmed: 'Confirmed',
      applied: 'Applied',
      withdrawn_by_user: 'Withdrawn by You',
      rejected_by_host: 'Rejected by Host',
      // Additional volunteer-specific statuses
      confirmed_volunteer: 'Confirmed as Volunteer',
      attended_volunteer: 'Attended as Volunteer',
      absent_volunteer: 'Absent Volunteer',
      cancelled_volunteer: 'Cancelled Volunteer Role',
      withdrawn_volunteer: 'Withdrawn Volunteer Application',
    },
    calendar: {
      title: 'My Calendar',
      empty: 'No events scheduled',
      switchView: 'Switch View',
    },
    empty: {
      title: 'No Events Yet',
      description: 'Start exploring and join some events!',
    },
    past: {
      title: 'Past Events',
      empty: 'No past events',
    },
    noEvents: "No Events",
    noUpcomingEvents: "No Upcoming Events",
    noPastEvents: "No Past Events",
    noDate: 'No Date',
    loading_title: 'Loading...',
    cancelRegistration: 'Cancel Registration',
    cancelConfirmTitle: 'Confirm Cancellation',
    cancelConfirmMessage: 'Are you sure you want to cancel this registration?',
    cancelConfirmTitle_registered: 'Confirm Cancellation',
    cancelConfirmMessage_registered: 'Are you sure you want to cancel this event registration?',
    cancelConfirmTitle_volunteer: 'Confirm Volunteer Cancellation',
    cancelConfirmMessage_volunteer: 'Are you sure you want to cancel your volunteer duties for this event?',
    cancelSuccess: 'Registration cancelled successfully.',
    cancelSuccess_registered: 'Event registration cancelled successfully.',
    cancelSuccess_volunteer: 'Volunteer application cancelled successfully.',
    cancelError: 'Failed to cancel registration. Please try again.',
    paymentStatusLabel: 'Payment',
    paymentStatus: {
      not_required: 'Not Required',
      pending: 'Pending',
      completed: 'Completed',
      failed: 'Failed',
      refunded: 'Refunded',
    },
    cancelRegistrationButton: 'Cancel Registration',
    withdrawApplicationButton: 'Withdraw Application',
    registered: 'Registered Events',
    volunteering: 'Volunteering Events',
    filter_by_date: 'Filter by Date',
    filter_by_type: 'Filter by Type',
    select_organization_loading: 'Loading organizations...',
    no_organization_selected: 'No organization selected',
  },
  resources: {
    title: 'Resource Details',
    files: 'Files',
    file: 'File',
    attachment: 'Attachment',
    lastUpdated: 'Last updated:',
    description: 'Description',
    download: 'Download',
    noResourcesTitle: 'No Resources Found',
    noResourcesText: 'There are no resources available at this time. Please check back later.',
    resourceNotFound: 'Resource not found',
    errorLoadingResource: 'Error loading resource',
    invalidResourceId: 'Invalid resource ID',
    invalidOrgId: 'Invalid organization ID',
    downloadError: "Error occurred while downloading the resource"
  },
  qrcode: {
    title: 'Member QR Code',
    subtitle: 'Show this QR code to check in at events',
    username: 'Username',
    memberId: 'Member ID',
    defaultUsername: 'Unknown User',
    loginRequired: 'Please login to view your QR code',
    scanHint: 'Present this QR code at event venues',
    error: 'Failed to generate QR code',
    retry: 'Try Again',
    tips: {
      title: 'Important Notice',
      content: 'To protect your privacy, please do not share your QR code with anyone other than our staff members. Contact us if you have any questions.'
    },
    verification: {
      validTitle: 'Valid QR Code',
      invalidTitle: 'Invalid QR Code',
      checkedInTitle: 'Successfully Checked In',
      memberFound: 'Member ID found:',
      approvePrompt: 'Would you like to check in this member?',
      checkedInPrompt: 'Member has been successfully checked in!',
      invalidMessage: 'The scanned QR code is not a valid member code.',
      loading: 'Loading event details...',
      checkIn: 'Check In',
      memberId: 'Member ID',
      memberName: 'Name',
      username: 'Username',
      phone: 'Phone',
      eventId: 'Event ID',
      eventName: 'Event Name',
      eventDate: 'Date & Time',
      eventLocation: 'Location',
      role: 'Role',
      status: 'Status'
    }
  },
  eventManagement: {
    title: 'Event Management',
    howToJoin: 'How to Join',
    steps: {
      arrival: 'Arrive at the event location',
      showQRCode: 'Show your QR code to the staff or volunteers',
    },
    showQRCode: 'Show QR Code',
    openQRCode: 'Open QR Code',
    qrCodeHint: 'You can also quickly access the QR code from the tab bar below',
    registrationInfo: 'Registration Information',
    registrationId: 'Registration ID',
    registrationDate: 'Registration Date',
    contactPerson: 'Contact Person',
    contactPhone: 'Contact Phone',
    contactEmail: 'Contact Email',
    specialNotes: 'Special Notes',
    clickToAddCalendar: 'Click the card to add to calendar',
    status: {
      title: "Event Status"
    },
    registrationStatusTitle: "Your Registration Status",
    error: {
      notFound: 'Registration details not found.',
      loadFailed: 'Failed to load registration details.',
      noId: 'Registration ID is missing.',
      locationOrTitleMissing: 'Event location or title is missing for map directions.'
    },
    cancelSuccessTitle: "Cancellation Successful",
    cancelSuccessMessage: "Your registration has been successfully cancelled.",
    cancelErrorTitle: "Cancellation Failed",
    cancelErrorMessage: "Could not cancel your registration. Please try again.",
    mapError: "Unable to Open Map",
    mapErrorDesc: "Cannot open map application. Please ensure you have a map app installed.",
    mapErrorDescSystem: "System error: Could not construct a valid map URL.",
    addToCalendar: "Add to Calendar",
    getDirections: "Get Directions",
    viewEventDetails: "View Event Details",
    cancelParticipation: "Cancel Participation",
    dateTime: "Date & Time",
    location: "Location",
    locationNotAvailable: "Location not available",
    cancelButton: "Cancel Registration",
    withdrawButton: "Withdraw Application",
    cancelConfirm: {
      title: "Confirm Cancellation",
      message: "Are you sure you want to cancel your registration for this event?"
    },
    withdrawConfirm: {
      title: "Confirm Withdrawal",
      message: "Are you sure you want to withdraw your volunteer application for this event?"
    },
    cancelReason: {
      title: "Cancellation Reason",
      placeholder: "Please provide a reason for cancellation (optional)"
    },
    cancelSuccess: {
      title: "Cancellation Successful",
      message: "Your registration has been successfully cancelled."
    },
    cancelError: {
      title: "Cancellation Failed",
      message: "Could not cancel your registration. Please try again."
    },
    withdrawSuccess: {
      title: "Withdrawal Successful",
      message: "Your volunteer application has been successfully withdrawn."
    },
    withdrawError: {
      title: "Withdrawal Failed",
      message: "Could not withdraw your application. Please try again."
    }
  },
  helpCenter: {
    searchPlaceholder: 'Search FAQ...',
    faq: {
      categories: {
        accountSecurity: 'Account Security',
        identityVerification: 'Identity Verification',
        qrCode: 'QR Code Usage',
        eventManagement: 'Event Management',
        notifications: 'Notifications'
      },
      items: {
        changePhone: {
          question: 'How do I change my phone number?',
          answer: 'Go to "Profile" page, tap "Account Settings", and select "Change Phone Number". Follow the verification steps to complete the change.\nIf you encounter any issues, please contact our customer service hotline at +852 2222 2234.'
        },
        smsIssue: {
          question: 'What if I cannot login with my original phone number?',
          answer: 'If you can no longer use your phone number and cannot receive verification codes or login to your account, please contact our customer service hotline at +852 2222 2234 immediately. Our customer service team will verify your identity and help you regain access to your account.'
        },
        accountStolen: {
          question: 'What should I do if my account is compromised?',
          answer: 'If you can still login to your account:\n1. Change your password immediately\n2. Contact our customer service hotline at +852 2222 2234 to report the situation\n\nIf you cannot login to your account:\nContact our customer service hotline at +852 2222 2234 directly, and we will freeze your account immediately and help you regain control.'
        },
        identityVerification: {
          question: 'How do I complete identity verification?',
          answer: 'Identity verification consists of two parts:\n\n1. ID Card Verification:\n• Upload photos of your Hong Kong ID card (front and back)\n• Wait for review (1-2 working days)\n\n2. Address Verification:\n• Upload any address proof document\n• Wait for review (1-2 working days)\n\nNote: Overall identity verification is complete only when both verifications are approved.'
        },
        verificationStatus: {
          question: 'How do I check my verification status?',
          answer: 'Check on the "Profile" page:\n\n1. ID Card Verification: Unverified/Under Review/Verified\n2. Address Verification: Unverified/Under Review/Verified\n\nOverall Identity Verification Status:\n• Unverified: Either verification incomplete\n• Verified: Both verifications approved'
        },
        volunteerVerification: {
          question: 'How do I become a volunteer?',
          answer: 'To become a volunteer, you need to:\n1. Complete identity verification first\n2. Click "Become a Volunteer" on the event details page\n3. Wait for volunteer qualification review (about 1-3 working days)\n\nOnce approved, you can register for volunteer activities. If you have any questions, please contact our customer service hotline at +852 2222 2234.'
        },
        qrCodeInfo: {
          question: 'What is the Member QR Code?',
          answer: 'The Member QR Code is your electronic identification at event venues. Each member has a unique QR code used to verify your identity with staff. Note that this is not an event ticket. You can quickly display it by tapping the QR code icon in the bottom navigation bar.'
        },
        qrCodeUsage: {
          question: 'How do I use the QR code at events?',
          answer: 'When you arrive at the event venue, tap the QR code icon in the bottom navigation bar and show your Member QR code to staff for identity verification.\nNote: The QR code is for identity verification only, not an event ticket.'
        },
        checkRegistered: {
          question: 'How do I check my registered events?',
          answer: 'Tap "My Events" in the bottom navigation bar to see all your registered events, including both participant and volunteer roles.'
        },
        cancelRegistration: {
          question: 'How do I cancel event registration?',
          answer: 'Go to "My Events", tap the event you want to cancel, and find the cancellation option at the bottom of the event management page. We recommend canceling at least 24 hours in advance to allow others to register.'
        },
        addCalendar: {
          question: 'How do I add an event to my calendar?',
          answer: 'In the event management page, tap the date section to add the event to your phone calendar for timely reminders.'
        },
        notifications: {
          question: 'How do I manage push notifications?',
          answer: 'In the "Profile" page, tap "Preferences" then "Notifications" to customize which types of notifications you receive, including event reminders and review notifications.'
        },
        addressVerification: {
          question: 'How do I complete address verification?',
          answer: '1. On the "Profile" page, tap "Address Verification"\n2. Upload one of the following documents:\n   • Bank Statement\n   • Utility Bill\n   • Mobile Phone Bill\n   • Other Documents\n\nDocument Requirements:\n• Must clearly show your name and address\n• In English or Chinese'
        }
      }
    },
    userEventStatus: {
      pending_approval: 'Pending Approval',
      registered: 'Registered',
      waitlisted: 'Waitlisted',
      rejected_approval: 'Approval Rejected',
      cancelled_by_user: 'Cancelled by You',
      cancelled_by_admin: 'Cancelled by Organizer',
      attended: 'Attended',
      absent: 'Absent',
    },
    eventOperationalStatus: {
      published: 'Published',
      archived: 'Archived',
      cancelled: 'Cancelled',
      draft: 'Draft',
      hidden: 'Hidden',
    },
  },
  privacyPolicy: {
    lastUpdated: 'Last Updated: January 16, 2025',
    sections: [
      {
        title: '1. Data Collection',
        content: 'We collect personal information including but not limited to: name, phone number, email address, Hong Kong ID information (for identity verification only), and event participation records. This information is used to provide services, improve user experience, ensure event safety, and fulfill our legal obligations.'
      },
      {
        title: '2. Data Usage',
        content: 'We use the collected information to: manage your account, process event registrations, send event reminders, conduct identity verification, provide customer support, and ensure platform security. We will not use your personal information for other purposes without your consent.'
      },
      {
        title: '3. Data Protection',
        content: 'We employ industry-standard encryption technologies and security measures to protect your personal information. Only authorized personnel can access this data, and access is limited to what is necessary to perform their job duties.'
      },
      {
        title: '4. Data Sharing',
        content: 'We do not share your personal information with third parties unless required by law or with your explicit consent. In some cases, we may share necessary information with event partners, but only to the extent required for event organization.'
      },
      {
        title: '5. Your Rights',
        content: 'You have the right to view, correct your personal information, and request account deletion. To exercise these rights, please contact us through customer service. Please note that some information may not be deleted due to legal requirements.'
      }
    ],
    footer: 'If you have any questions about our Privacy Policy, please contact our Customer Service Department.'
  },
  termsOfService: {
    lastUpdated: 'Last Updated: January 16, 2025',
    introduction: 'Please read the following terms carefully as they constitute a legally binding agreement between you and us.',
    sections: [
      {
        title: '1.Service Scope',
        content: 'We provide membership management, event registration, and identity verification services. You need to complete identity verification to access full membership features, including event registration and becoming a volunteer. We reserve the right to modify, suspend, or terminate services at any time.'
      },
      {
        title: '2.Membership Eligibility',
        content: 'You must be hold a valid Hong Kong ID card to register as a member. You should provide true, accurate, and complete personal information and update it promptly when changes occur.'
      },
      {
        title: '3.Identity Verification',
        content: 'To ensure service quality and security, you need to complete the identity verification process. This includes uploading your Hong Kong ID card photo and other necessary documents. We will review your submitted information and reserve the right to reject verification.'
      },
      {
        title: '4.Event Participation',
        content: 'You can register for events published on the platform. Once registered, you should comply with the specific rules and requirements of the event. Please notify the organizer in advance if you need to cancel. For volunteer positions, you must complete identity verification before registration.'
      },
      {
        title: '5.Account Security',
        content: 'You are responsible for maintaining the security of your account and password. Do not share your account with others. If you suspect your account has been compromised, contact customer service immediately. You should change your password regularly and ensure your contact information, such as phone number, remains valid.'
      },
      {
        title: '6.Privacy Protection',
        content: 'We value your privacy and protect your personal information according to our Privacy Policy. We will only use your information within necessary scope and will not disclose your personal information to third parties without your consent.'
      },
      {
        title: '7.Disclaimer',
        content: 'We are not liable for service interruptions due to network issues, system maintenance, force majeure, or other reasons. For potential accidents or losses during events, we recommend purchasing appropriate insurance coverage.'
      }
    ],
    footer: 'If you have any questions about these Terms of Service, please contact our customer service team. Phone: +852 2222 2234, Email: <EMAIL>'
  },
  category: {
    title: 'Categories',
    community: 'Community Service',
    technology: 'Technology Innovation',
    education: 'Education Development',
    culture: 'Arts & Culture',
    environment: 'Environmental Protection',
    health: 'Healthcare',
    business: 'Business',
    design: 'Design',
    marketing: 'Marketing',
    infrastructure: 'Infrastructure',
    youth: 'Youth Development',
  },
  notifications: {
    title: 'Notification Settings',
    channels: {
      title: 'Notification Channels',
      app: 'App Notifications',
      appDescription: 'Receive notifications through the app',
      whatsapp: 'WhatsApp Notifications',
      whatsappDescription: 'Receive notifications through WhatsApp',
    },
    types: {
      title: 'Notification Types',
      events: 'Event Reminders',
      eventsDescription: 'Receive event reminders and change notifications',
      posts: 'News Updates',
      postsDescription: 'Receive latest news and announcements',
      promotional: 'Promotional Messages',
      promotionalDescription: 'Receive promotional offers and messages',
    },
  },
  elderlyMode: {
    title: 'Elderly Mode',
    description: 'Enable large text and other accessibility features',
    fontSize: 'Font Size',
    fontSizeDescription: 'Choose a comfortable font size for better readability',
    note: 'Note: Font size changes will take effect after restarting the app',
    options: {
      standard: 'Standard',
      larger: 'Larger',
      large: 'Large',
      extraLarge: 'Extra Large',
    },
  },
  elderlySettings: {
    title: 'Display Settings',
    selectMode: 'Please Select Display Mode',
    modes: {
      normal: {
        label: 'Standard Mode',
        desc: 'Standard font and interface'
      },
      senior: {
        label: 'Senior Mode',
        desc: 'Enlarged font and simplified interface'
      }
    },
    preview: {
      title: 'Preview',
      fontSize: 'Font Size: {{size}}pt',
      event: {
        free: 'FREE',
        attendees: '{{count}} attendees'
      }
    }
  },
  applications: {
    status: {
      header: 'Application Status Overview',
      title: 'Application Status',
      subtitle: 'Apply and check the status of your various programs and services.',
      info: {
        title: 'Important Information',
        text: 'Applications typically take 1-3 business days to process. You will be notified when your application status is updated.'
      },
      verified: 'Verified',
      pending: 'Under Review',
      rejected: 'Rejected',
      unverified: 'Not Applied',
      error: 'Error',
      documents: 'Required Documents',
      reason: 'Reason',
    },
    prerequisites: {
      hkidRequired: {
        title: 'HKID Verification Required',
        message: 'To apply for this service, your Hong Kong ID card must be verified first. Please complete HKID verification.',
        short: 'HKID verification needed',
        verifyNow: 'Verify Now'
      }
    },
    types: {
      hkid: 'Hong Kong ID Card',
      mainlandId: 'Mainland ID Card',
      mainlandTravelPermit: 'Home Return Permit or Mainland Travel Permit',
      passport: 'Passport',
      hkYouth: 'HK Youth+',
      address: 'Address Verification',
      studentId: 'Student ID Card',
      homeVisit: 'Home Visit Program',
      volunteer: 'Volunteer Program',
    },
    requirements: {
      identityDocument: 'Identity Document Verification',
      addressVerification: 'Address Verification'
    },
    homeVisit: {
      title: 'Home Visit Program',
      description: 'Apply for assistance with daily tasks and regular home visits by our volunteers'
    },
    volunteer: {
      title: 'Volunteer Program',
      description: 'Apply to become a volunteer to help others in the community',
      status: {
        title: 'Volunteer Applications',
        subtitle: 'View the status of your volunteer applications across different organizations',
        organizations: 'Organizations',
        applications: 'Current Applications',
        availableOrganizations: 'Available Organizations',
        approved: 'Approved',
        pending: 'Under Review',
        rejected: 'Rejected',
        notApplied: 'Not Applied',
        error: 'Error',
        noOrganizations: 'No organizations available',
        noApplications: 'No volunteer applications submitted yet',
        noOrganizationsAvailable: 'No more organizations available to apply',
        unknownOrganization: 'Unknown Organization'
      }
    },
    forms: {
      common: {
        softCopy: 'Soft Copy',
        chineseName: 'Chinese Name',
        chineseCommercialCode: 'Chinese Commercial Code',
        englishName: 'English Name',
        gender: 'Gender',
        dateOfBirth: 'Date of Birth',
        expiryDate: 'Expiry Date',
        issueDate: 'Issue Date',
        documentNumber: 'Document Number',
        permanentResident: 'Permanent Resident',
        yes: 'Yes',
        no: 'No',
        male: 'Male',
        female: 'Female',
      },
      hkid: {
        title: 'Hong Kong ID Card Verification',
        idNumber: 'ID Card Number',
      },
      mainlandId: {
        title: 'Mainland ID Card Verification',
        idNumber: 'ID Card Number',
      },
      mainlandTravelPermit: {
        title: 'Home Return Permit/Mainland Travel Permit Verification',
        permitNumber: 'Permit Number',
      },
      passport: {
        title: 'Passport Verification',
        passportNumber: 'Passport Number',
        issuingCountry: 'Issuing Country',
      },
      hkYouth: {
        title: 'HK Youth+ Verification',
        membershipNumber: 'Membership Number',
      },
      address: {
        title: 'Address Verification',
        unit: 'Unit',
        floor: 'Floor',
        building: 'Building Name/Number',
        street: 'Street Address',
        district: 'District',
        region: 'Region',
      },
      studentId: {
        title: 'Student ID Verification',
        schoolName: 'School Name',
        grade: 'Grade',
        expiryDate: 'Expiry Date',
      },
    },
    errors: {
      organizationRequired: 'Please select an organization',
      fetchOrganizationsError: 'Failed to fetch organizations',
      noOrganizationsAvailable: 'No organizations available',
      noOrganizationSelected: 'No organization selected',
      organizationNotFound: 'Organization not found',
      failed_to_load_registered_events: 'Failed to load registered events',
      failed_to_load_volunteer_applications: 'Failed to load volunteer applications',
      failed_to_load_events: 'Failed to load events',
    },
  },
  programApplications: {
    title: "Program Applications",
    subtitle: "Apply and check the status of your various programs and services.",
    labels: {
      status: "Application Status"
    },
    status: {
      approved: "Approved",
      pending: "Under Review",
      rejected: "Rejected",
      unverified: "Not Applied",
      error: "Error"
    },
    messages: {
      approved: "Your application has been approved. You can browse events and apply to be a volunteer. To update verification information, please contact customer service to reset verification status.",
      pending: "Your verification application is being processed, which usually takes 3-5 business days. We will notify you of the review result by email.",
      rejected: "Your application has been rejected. Please check the rejection reason.",
      unverified: "You have not applied for this program yet.",
      volunteerSubmitSuccess: "Volunteer application successfully submitted!",
      homeVisitSubmitSuccess: "Home Visit Program application successfully submitted!",
      verificationRequired: "Identity and address verification required before application",
      verificationRequiredTitle: "Verification Required",
      verificationRequiredDesc: "You must complete ID card and address verification before applying for the program.",
      duplicateVolunteerOrgApplication: "You have already applied to become a volunteer for this organization.",
      submitError: "Application submission failed. Please try again later."
    },
    buttons: {
      applyNow: "Apply Now",
      completeVerification: "Complete Verification"
    },
    volunteer: {
      title: "Volunteer Program",
      description: "Apply to become a volunteer to bring warmth and assistance to community events.",
      selectOrganization: "Select Organization",
      pleaseSelectOrg: "Please select an organization",
      organization: "Current Application Organization"
    },
    homeVisit: {
      title: "Home Visit Program",
      description: "Apply for our home visit program to receive care in your home."
    },
    modal: {
      userInformation: "Your Information",
      termsAndConditions: "Terms and Conditions",
      agreeToTerms: "I have read and agree to the terms and conditions",
      submit: "Submit Application",
      messages: {
        agreementRequired: "Please agree to the terms and conditions before submitting"
      },
      terms: {
        responsibilities: "Responsibilities",
        requirements: "Requirements",
        benefits: "Benefits",
        eligibility: "Eligibility Criteria",
        services: "Services Provided",
        frequency: "Visit Frequency",
        policy: "Privacy Policy",
        volunteerResponsibilities: "As a volunteer, you will assist with event management tasks, including helping users sign in, maintaining event order, etc.",
        volunteerRequirements: "Volunteers must complete identity verification and comply with event management regulations.",
        volunteerBenefits: "Volunteers will have the opportunity to serve the community and accumulate event management experience.",
        homeVisitEligibility: "To be eligible for home visits, you must be a registered member, have verified identity, and have documented assistance needs.",
        homeVisitServices: "Our team can assist with basic household chores, provide necessities, and offer companionship services.",
        homeVisitFrequency: "Visit frequency is determined based on individual needs, typically ranging from weekly to monthly.",
        homeVisitPolicy: "All personal information shared during the application process and visits will be kept confidential."
      }
    }
  },
  eventDetails: {
    missingVerificationsError: "To participate in this event, you need the following approved verifications: {{verifications}}.",
    pendingVerificationsWarning: "The following verifications are still pending approval: {{verifications}}. You can register once they are approved.",
    verificationRequiredTitle: "Verification Required",
    goToVerification: "Go to Verification",
    openMap: 'Open in Maps',
    chooseMapApp: 'Choose your preferred map application',
    eventNotLoaded: 'Event details not loaded. Please try again.',
    registrationFailedUnknown: 'Registration failed. Please try again later.',
    registrationFailedParticipant: 'You are already registered as a participant for this event.',
    registrationFailedVolunteer: 'You are already registered as a volunteer for this event.',
    title: 'Event Details',
    attendees: 'Attendees',
    maxWaitingList: 'Max Waitlist'
  },
  verificationTypes: {
    hk_id_card: 'HK ID Card',
    mainland_china_id_card: 'Mainland China ID Card',
    passport: 'Passport',
    student_id: 'Student ID',
    address_proof: 'Address Proof',
    mainland_travel_permit: 'Home Return Permit/Mainland Travel Permit',
    hk_youth_plus: 'HK Youth+',
  },
  apiErrors: {
    // General
    Error: 'An unexpected error occurred. Please try again.',
    NetworkError: 'A network error occurred. Please check your connection.',
    DefaultError: 'An error occurred.',

    // Event Registration Errors (from plan 2.2, ExploreAPI.registerAsParticipant)
    ErrMissingEventId: 'Event ID is missing.',
    ErrInvalidUUID: 'The provided ID is invalid.',
    ErrUserNotVerifiedForRequiredTypes: 'You do not meet the verification requirements for this event.',
    ErrRegistrationClosed: 'Registration for this event is closed.',
    ErrEventNotFound: 'Event not found.',
    ErrAlreadyRegistered: 'You are already registered for this event.',
    ErrAlreadyWaitlisted: 'You are already on the waitlist for this event.',
    ErrEventCapacityReached: 'This event has reached its capacity.',
    ErrWaitlistFull: 'The waitlist for this event is full.',
    EventTimeConflictError: 'This event conflicts with another event you are registered for.',

    // Event Cancellation Errors (from plan 2.2, MyEventsAPI.cancelEventRegistration)
    ErrInvalidRegistrationId: 'Invalid registration ID format.',
    ErrUserNotAuthorizedToCancel: 'You are not authorized to cancel this registration.',
    ErrRegistrationNotFound: 'Registration not found.',
    ErrRegistrationAlreadyCancelled: 'This registration has already been cancelled.',
    ErrRegistrationNotCancellable: 'This registration cannot be cancelled at this time (e.g., event started or too close to start date).',
    
    // Volunteer Application Specific (might be similar to registration)
    ErrVolunteerApplicationFailed: 'Failed to submit volunteer application.',
    ErrAlreadyVolunteered: 'You have already applied to volunteer for this event.',
    ErrWithdrawVolunteerFailed: 'Failed to withdraw volunteer application.',

    // Add other common error codes as they are identified
    // Example: err.code might be 'ErrDatabase' or similar generic backend errors
  },
  sharing: {
    shareFailedError: 'An error occurred while trying to share the event'
  },
  orgRoles: {
    admin: 'Admin',
    member: 'Member',
    // Add other roles as needed
  },
  verification: {
    pending: {
      title: 'Verification Pending',
      message: 'Your verification is currently under review. We will notify you once it is complete.',
    }
  },
  camera: {
    permissionDenied: {
      title: 'Camera Permission Denied',
      message: 'Please allow camera access in settings to scan QR codes'
    },
    requestingPermission: 'Requesting camera permission...',
    noAccess: 'No camera access'
  },
  volunteer: {
    assistOthers: 'Assist Others',
    scanInstructions: 'Point your camera at the member\'s QR code to assist them with identity verification application',
    proceedWithApplication: 'Proceed with Application',
    proceedWithVolunteerApplication: 'Proceed with Volunteer Application',
    memberFound: {
      title: 'Member Found',
      message: 'Do you want to proceed with identity verification application for this member?',
      volunteerMessage: 'Do you want to proceed with volunteer application assistance for this member?',
      memberId: 'Member ID'
    },

    assistingApplications: {
      title: 'Assisting Identity Verification',
      subtitle: 'Viewing identity verification status for member {{memberId}}',
      volunteer: {
        title: 'Assisting Volunteer Applications',
        subtitle: 'Viewing volunteer application status for member {{memberId}}'
      }
    },
  }
};